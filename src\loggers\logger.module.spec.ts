import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LoggerModule } from './logger.module';
import { LoggerService } from './logger.service';
import { LoggerModule as PinoLoggerModule } from 'nestjs-pino';

// Mock external dependencies
jest.mock('nestjs-pino', () => ({
  LoggerModule: {
    forRootAsync: jest.fn(() => ({
      module: class MockPinoLoggerModule {},
      providers: [],
      exports: []
    }))
  }
}));

jest.mock('pino-noir', () => jest.fn(() => jest.fn()));

jest.mock('./logger.service', () => ({
  LoggerService: jest.fn().mockImplementation(() => ({
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }))
}));

describe('LoggerModule', () => {
  let module: TestingModule;
  let loggerModule: LoggerModule;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config = {
        LOG_LEVEL: 'info',
        NODE_ENV: 'test'
      };
      return config[key] || 'default-value';
    })
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [LoggerModule],
      providers: [
        {
          provide: ConfigService,
          useValue: mockConfigService
        }
      ]
    }).compile();

    loggerModule = module.get<LoggerModule>(LoggerModule);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    jest.clearAllMocks();
  });

  describe('Module instantiation', () => {
    it('should be defined', () => {
      expect(loggerModule).toBeDefined();
    });

    it('should be an instance of LoggerModule', () => {
      expect(loggerModule).toBeInstanceOf(LoggerModule);
    });

    it('should have Global decorator', () => {
      const globalMetadata = Reflect.getMetadata('__global__', LoggerModule);
      expect(globalMetadata).toBe(true);
    });
  });

  describe('Module configuration', () => {
    it('should have correct module metadata', () => {
      const moduleMetadata = Reflect.getMetadata('imports', LoggerModule) || [];
      expect(moduleMetadata).toBeDefined();
      expect(Array.isArray(moduleMetadata)).toBe(true);
    });

    it('should import PinoLoggerModule', () => {
      const imports = Reflect.getMetadata('imports', LoggerModule) || [];
      expect(imports).toHaveLength(1);
      // The import is a dynamic module created by forRootAsync
      expect(imports[0]).toBeDefined();
    });

    it('should have LoggerService as provider', () => {
      const providers = Reflect.getMetadata('providers', LoggerModule) || [];
      expect(providers).toContain(LoggerService);
    });

    it('should export LoggerService and PinoLoggerModule', () => {
      const exports = Reflect.getMetadata('exports', LoggerModule) || [];
      expect(exports).toContain(LoggerService);
      expect(exports).toContain(PinoLoggerModule);
    });
  });

  describe('PinoLoggerModule configuration', () => {
    it('should call PinoLoggerModule.forRootAsync', () => {
      expect(PinoLoggerModule.forRootAsync).toHaveBeenCalled();
    });

    it('should configure PinoLoggerModule with correct options', () => {
      const callArgs = (PinoLoggerModule.forRootAsync as jest.Mock).mock.calls[0][0];
      
      expect(callArgs).toHaveProperty('imports');
      expect(callArgs).toHaveProperty('useFactory');
      expect(callArgs).toHaveProperty('inject');
      expect(callArgs.inject).toContain(ConfigService);
    });

    it('should use ConfigModule in imports', () => {
      const callArgs = (PinoLoggerModule.forRootAsync as jest.Mock).mock.calls[0][0];
      expect(callArgs.imports).toBeDefined();
      expect(Array.isArray(callArgs.imports)).toBe(true);
    });

    it('should inject ConfigService', () => {
      const callArgs = (PinoLoggerModule.forRootAsync as jest.Mock).mock.calls[0][0];
      expect(callArgs.inject).toContain(ConfigService);
    });
  });

  describe('Factory function', () => {
    let factoryFunction: Function;

    beforeEach(() => {
      const callArgs = (PinoLoggerModule.forRootAsync as jest.Mock).mock.calls[0][0];
      factoryFunction = callArgs.useFactory;
    });

    it('should be an async function', () => {
      expect(factoryFunction.constructor.name).toBe('AsyncFunction');
    });

    it('should return pino configuration', async () => {
      const result = await factoryFunction(mockConfigService);
      
      expect(result).toHaveProperty('pinoHttp');
      expect(result.pinoHttp).toBeInstanceOf(Object);
    });

    it('should configure log level from ConfigService', async () => {
      mockConfigService.get.mockReturnValue('debug');
      
      const result = await factoryFunction(mockConfigService);
      
      expect(mockConfigService.get).toHaveBeenCalledWith('LOG_LEVEL');
      expect(result.pinoHttp.level).toBe('debug');
    });

    it('should configure serializers', async () => {
      const result = await factoryFunction(mockConfigService);
      
      expect(result.pinoHttp).toHaveProperty('serializers');
      expect(typeof result.pinoHttp.serializers).toBe('function');
    });

    it('should configure quietReqLogger', async () => {
      const result = await factoryFunction(mockConfigService);
      
      expect(result.pinoHttp.quietReqLogger).toBe(true);
    });

    it('should configure formatters', async () => {
      const result = await factoryFunction(mockConfigService);
      
      expect(result.pinoHttp).toHaveProperty('formatters');
      expect(result.pinoHttp.formatters).toHaveProperty('level');
      expect(typeof result.pinoHttp.formatters.level).toBe('function');
    });

    it('should configure redact options', async () => {
      const result = await factoryFunction(mockConfigService);
      
      expect(result.pinoHttp).toHaveProperty('redact');
      expect(result.pinoHttp.redact).toHaveProperty('paths');
      expect(result.pinoHttp.redact).toHaveProperty('censor');
      expect(result.pinoHttp.redact).toHaveProperty('remove');
      expect(result.pinoHttp.redact.paths).toContain('pid');
      expect(result.pinoHttp.redact.paths).toContain('hostname');
      expect(result.pinoHttp.redact.censor).toBe('[PINO REDACTED]');
      expect(result.pinoHttp.redact.remove).toBe(true);
    });

    it('should handle different log levels', async () => {
      const logLevels = ['fatal', 'error', 'warn', 'info', 'debug', 'trace', 'silent'];
      
      for (const level of logLevels) {
        mockConfigService.get.mockReturnValue(level);
        const result = await factoryFunction(mockConfigService);
        expect(result.pinoHttp.level).toBe(level);
      }
    });
  });

  describe('Formatters', () => {
    let levelFormatter: Function;

    beforeEach(async () => {
      const callArgs = (PinoLoggerModule.forRootAsync as jest.Mock).mock.calls[0][0];
      const factoryFunction = callArgs.useFactory;
      const result = await factoryFunction(mockConfigService);
      levelFormatter = result.pinoHttp.formatters.level;
    });

    it('should format level to uppercase', () => {
      expect(levelFormatter('info')).toEqual({ level: 'INFO' });
      expect(levelFormatter('error')).toEqual({ level: 'ERROR' });
      expect(levelFormatter('debug')).toEqual({ level: 'DEBUG' });
      expect(levelFormatter('warn')).toEqual({ level: 'WARN' });
    });

    it('should handle different case inputs', () => {
      expect(levelFormatter('INFO')).toEqual({ level: 'INFO' });
      expect(levelFormatter('Error')).toEqual({ level: 'ERROR' });
      expect(levelFormatter('DeBuG')).toEqual({ level: 'DEBUG' });
    });
  });

  describe('Providers', () => {
    it('should have LoggerService available in module', () => {
      const loggerService = module.get<LoggerService>(LoggerService);
      expect(loggerService).toBeDefined();
      expect(loggerService).toBeInstanceOf(LoggerService);
    });

    it('should register exactly one provider', () => {
      const providers = Reflect.getMetadata('providers', LoggerModule) || [];
      expect(providers).toHaveLength(1);
    });
  });

  describe('Exports', () => {
    it('should export LoggerService', () => {
      const exports = Reflect.getMetadata('exports', LoggerModule) || [];
      expect(exports).toContain(LoggerService);
    });

    it('should export PinoLoggerModule', () => {
      const exports = Reflect.getMetadata('exports', LoggerModule) || [];
      expect(exports).toContain(PinoLoggerModule);
    });

    it('should export exactly two items', () => {
      const exports = Reflect.getMetadata('exports', LoggerModule) || [];
      expect(exports).toHaveLength(2);
    });
  });

  describe('Module lifecycle', () => {
    it('should initialize without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [LoggerModule],
        providers: [
          {
            provide: ConfigService,
            useValue: mockConfigService
          }
        ]
      }).compile();

      await expect(testModule.init()).resolves.not.toThrow();
      await testModule.close();
    });

    it('should close without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [LoggerModule],
        providers: [
          {
            provide: ConfigService,
            useValue: mockConfigService
          }
        ]
      }).compile();

      await testModule.init();
      await expect(testModule.close()).resolves.not.toThrow();
    });
  });

  describe('Global module behavior', () => {
    it('should be available globally', () => {
      const globalMetadata = Reflect.getMetadata('__global__', LoggerModule);
      expect(globalMetadata).toBe(true);
    });

    it('should make LoggerService available globally', async () => {
      // This test verifies that the module is properly configured as global
      const testModule = await Test.createTestingModule({
        imports: [LoggerModule],
        providers: [
          {
            provide: ConfigService,
            useValue: mockConfigService
          }
        ]
      }).compile();

      const loggerService = testModule.get<LoggerService>(LoggerService);
      expect(loggerService).toBeDefined();
      
      await testModule.close();
    });
  });

  describe('Error scenarios', () => {
    it('should handle missing ConfigService gracefully', async () => {
      // Test module creation without ConfigService
      const testModule = await Test.createTestingModule({
        imports: [LoggerModule]
      }).compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });

    it('should handle ConfigService returning undefined', async () => {
      const mockConfigWithUndefined = {
        get: jest.fn(() => undefined)
      };

      const callArgs = (PinoLoggerModule.forRootAsync as jest.Mock).mock.calls[0][0];
      const factoryFunction = callArgs.useFactory;
      
      const result = await factoryFunction(mockConfigWithUndefined);
      expect(result.pinoHttp.level).toBeUndefined();
    });
  });

  describe('Type safety', () => {
    it('should have correct TypeScript types', () => {
      expect(loggerModule).toBeInstanceOf(Object);
      expect(typeof LoggerModule).toBe('function');
    });

    it('should be a proper NestJS module', () => {
      const moduleDecorator = Reflect.getMetadata('__module__', LoggerModule);
      expect(moduleDecorator).toBeDefined();
    });
  });
});
