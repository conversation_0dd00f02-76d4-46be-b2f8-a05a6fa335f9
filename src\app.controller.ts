import { Controller, Get, Version, VERSION_NEUTRAL } from '@nestjs/common';
import { AppService } from './app.service';
import { AboutDto } from './about.response.dto';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Version(VERSION_NEUTRAL)
  @Get('about')
  async getServiceDetails(): Promise<AboutDto> {
    return await this.appService.getServiceDetails();
  }
}
