import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { AlethiaService } from './alethia.service';
import {
  AlethiaRequestDto,
  CONTACT,
  DeviceOwnerRequestDto,
  ORGANIZATION,
} from './dto/alethia.request.dto';
import { LoggerService } from '../loggers/logger.service';
import axios from 'axios';
import { withResponseErrorHandler } from '../utils/helpers';

@Injectable()
export class AlethiaWrapperService {
  constructor(
    private readonly alethiaService: AlethiaService,
    private readonly logger: LoggerService,
  ) {}

  async getOwners(data: AlethiaRequestDto) {
    try {
      this.logger.log('getOwners method in alethia wrapper service');
      const response = await this.alethiaService.getDeviceOwnersFromAlethia(
        data.devices,
        data.topologyData,
      );

      const organizationResponse = await this.identifyOrganization(
        data.devices,
        response,
      );
      this.logger.log('Organization response ', organizationResponse);
      return organizationResponse;
    } catch (error) {
      this.logger.log(
        `error while identifying owner for : ${JSON.stringify(data?.devices)}`,
      );
      let errorMsg;
      if (error?.response && error?.response?.data?.errors?.length) {
        errorMsg = {
          errorCode: error?.response?.data?.errors[0]?.errorcode,
          data: { message: error?.response?.data?.errors[0]?.message },
        };
      } else {
        this.logger.error(error, 'Error in createOrg');
      }

      this.logger.error(errorMsg, 'errorMsg');
    }
  }

  async identifyOrganization(
    devices,
    response,
  ): Promise<DeviceOwnerRequestDto[]> {
    this.logger.log(`Identify organization.`);

    const deviceOwnerResponse = response?.data?.allFiremonDevices.nodes;
    const ownerMap = {};
    deviceOwnerResponse?.forEach((item) => {
      ownerMap[item.hostname] = item.contact;
    });

    devices.forEach((device) => {
      device.target.forEach(
        (item) => (item['owner'] = ownerMap[item.name] || 'owner not found'),
      );
    });

    for (const topologyData of devices) {
      for (const workflow of topologyData.target) {
        this.logger.log('hostname:', workflow?.name);
        this.logger.log('owner', workflow?.owner);
        if (
          workflow?.owner?.toLowerCase().includes(CONTACT.APS) ||
          workflow?.owner?.toLowerCase().includes(CONTACT.DC)
        ) {
          workflow['organization'] = ORGANIZATION.RED_APS;
        } else if (workflow?.owner?.toLowerCase().includes(CONTACT.CBO)) {
          workflow['organization'] = ORGANIZATION.RED_CBO;
        } else if (await this.checkForBlue(workflow)) {
          workflow['organization'] = ORGANIZATION.CORPORATE;
        } else {
          workflow['organization'] = ORGANIZATION.UNKNOWN;
        }
        this.logger.log('Assigned organization:', workflow['organization']);
      }
    }

    return devices;
  }

  private async checkForBlue(workflow): Promise<boolean> {
    let blue;
    let blue_flag = false;
    try {
      const blue_hostname = workflow.name.split('.')[0];
      const stripped_hostname = blue_hostname.replace('(Cluster)', '');
      const blue_sot = `${process.env.BLUE_SOT_URL}?hostname=${stripped_hostname}`;
      blue = await withResponseErrorHandler(axios.get(blue_sot));
      this.logger.log('blue', blue);
    } catch (error) {
      this.logger.log(`error from blue sot: ${workflow.name}`, error.stack);
    }
    if (
      blue?.status === 'SUCCESS' &&
      blue?.cmdb?.length &&
      (blue?.cmdb[0]?.company?.toLowerCase().includes('blue') ||
        workflow?.owner?.toLowerCase().includes(CONTACT.INF))
    ) {
      const isOwnerNotFound = workflow['owner'] === 'owner not found';
      if (isOwnerNotFound)
        workflow['owner'] = blue.cmdb[0].supportedbyteam;
      blue_flag = true;
    }
    return blue_flag;
  }
}
