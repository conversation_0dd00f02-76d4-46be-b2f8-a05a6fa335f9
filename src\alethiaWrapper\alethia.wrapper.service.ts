import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { AlethiaService } from './alethia.service';
import {
  AlethiaRequestDto,
  CONTACT,
  DeviceOwnerRequestDto,
  ORGANIZATION,
} from './dto/alethia.request.dto';
import { LoggerService } from '../loggers/logger.service';
import axios from 'axios';
import { withResponseErrorHandler } from '../utils/helpers';
import axiosRetry from 'axios-retry';
import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../utils/constants';

@Injectable()
export class AlethiaWrapperService {
  constructor(
    private readonly alethiaService: AlethiaService,
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
  ) {}

  async getOwners(data: AlethiaRequestDto) {
    try {
      this.logger.log('getOwners method in alethia wrapper service');
      const response = await this.alethiaService.getDeviceOwnersFromAlethia(
        data.devices,
        data.topologyData,
      );

      const organizationResponse = await this.identifyOrganization(
        data.devices,
        response,
      );
      this.logger.log('Organization response ', organizationResponse);
      return organizationResponse;
    } catch (error) {
      this.logger.log(
        `error while identifying owner for : ${JSON.stringify(data?.devices)}`,
      );
      let errorMsg;
      if (error?.response && error?.response?.data?.errors?.length) {
        errorMsg = {
          errorCode: error?.response?.data?.errors[0]?.errorcode,
          data: { message: error?.response?.data?.errors[0]?.message },
        };
      } else {
        this.logger.error(error, 'Error in createOrg');
      }

      this.logger.error(errorMsg, 'errorMsg');
    }
  }

  async identifyOrganization(
    devices,
    response,
  ): Promise<DeviceOwnerRequestDto[]> {
    this.logger.log(`Identify organization.`);

    const deviceOwnerResponse = response?.data?.allFiremonDevices.nodes;
    const ownerMap = {};
    deviceOwnerResponse?.forEach((item) => {
      ownerMap[item.hostname] = item.contact;
    });

    devices.forEach((device) => {
      device.target.forEach(
        (item) => (item['owner'] = ownerMap[item.name] || 'owner not found'),
      );
    });

    for (const topologyData of devices) {
      for (const workflow of topologyData.target) {
        this.logger.log('hostname:', workflow?.name);
        this.logger.log('owner', workflow?.owner);
        if (
          workflow?.owner?.toLowerCase().includes(CONTACT.APS) ||
          workflow?.owner?.toLowerCase().includes(CONTACT.DC)
        ) {
          workflow['organization'] = ORGANIZATION.RED_APS;
        } else if (workflow?.owner?.toLowerCase().includes(CONTACT.CBO)) {
          workflow['organization'] = ORGANIZATION.RED_CBO;
        } else if (await this.checkForBlue(workflow)) {
          workflow['organization'] = ORGANIZATION.CORPORATE;
        } else {
          workflow['organization'] = ORGANIZATION.UNKNOWN;
        }
        this.logger.log('Assigned organization:', workflow['organization']);
      }
    }

    return devices;
  }

  private async checkForBlue(workflow): Promise<boolean> {
    let blue;
    let blue_flag = false;
    let stripped_hostname;
    try {
      const enableRetry = this.configService.get(
        ENVIRONMENT_VARS.ENABLE_BLUE_SOT_RETRY,
      );
      const blue_hostname = workflow.name.split('.')[0];
      stripped_hostname = blue_hostname.replace('(Cluster)', '');
      const axiosInstance = axios.create({ baseURL: process.env.BLUE_SOT_URL });
      //Intentionally kept retrycondition as true, to run for all http exceptions
      axiosRetry(axiosInstance, {
        retries:
          this.configService.get(
            ENVIRONMENT_VARS.BLUE_SOT_API_MAX_RETRY_COUNT,
          ) || 3,
        retryCondition: () => enableRetry,
        retryDelay: axiosRetry.exponentialDelay,
        onRetry: (retryCount, error, requestConfig) =>
          this.logger.log(
            `retrying blue sot api for ${stripped_hostname} with count ${retryCount}`,
          ),
      });
      blue = await withResponseErrorHandler(
        axiosInstance.get('', { params: { hostname: stripped_hostname } }),
      );
      this.logger.log('blue', blue);
    } catch (error) {
      this.logger.log(`error from blue sot: ${workflow.name}`, error.stack);
    }
    if (
      blue?.status === 'SUCCESS' &&
      blue?.cmdb?.length &&
      blue?.cmdb[0]?.company?.toLowerCase().includes('blue')
    ) {
      this.logger.log(`${stripped_hostname} is identified by blue sot`);
      const isOwnerNotFound = workflow['owner'] === 'owner not found';
      if (isOwnerNotFound && blue?.cmdb[0]?.supportedbyteam)
        workflow['owner'] = blue.cmdb[0].supportedbyteam;
      blue_flag = true;
    } else if (workflow?.owner?.toLowerCase().includes(CONTACT.INF)) {
      this.logger.log(`${stripped_hostname} is identified by alethia`);
      blue_flag = true;
    } else {
      this.logger.log(
        `${stripped_hostname} is not identified by blue sot and alethia`,
      );
      blue_flag = false;
    }

    return blue_flag;
  }
}
