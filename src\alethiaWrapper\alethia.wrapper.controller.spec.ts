import { Test, TestingModule } from '@nestjs/testing';
import { AlethiaWrapperController } from './alethia.wrapper.controller';
import { AlethiaWrapperService } from './alethia.wrapper.service';
import { AlethiaService } from './alethia.service';

import { LoggerService } from '../loggers/logger.service';

type MockAlethiaWrapperService = Partial<
  Record<keyof AlethiaWrapperService, jest.Mock>
>;
type MockAlethiaService = Partial<Record<keyof AlethiaService, jest.Mock>>;
describe('AlethiaWrapperController', () => {
  let controller: AlethiaWrapperController;
  let mockAlethiaWrapperService: MockAlethiaWrapperService;
  let mockAlethiaService: MockAlethiaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AlethiaWrapperController],
      providers: [
        LoggerService,
        {
          provide: AlethiaWrapperService,
          useFactory: () => {
            const mockAlethiaWrapperService: MockAlethiaWrapperService = {
              getOwners: jest.fn(),
            };
            return mockAlethiaWrapperService;
          },
        },
        {
          provide: AlethiaService,
          useFactory: () => {
            const mockAlethiaService: MockAlethiaService = {
              getDeviceOwnersFromAlethia: jest.fn(),
            };
            return mockAlethiaService;
          },
        },
      ],
    }).compile();

    controller = module.get<AlethiaWrapperController>(AlethiaWrapperController);
    mockAlethiaWrapperService = module.get<MockAlethiaWrapperService>(
      AlethiaWrapperService,
    );
    mockAlethiaService = module.get<MockAlethiaService>(AlethiaService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('Get Owners from Alethia Service', () => {
    it('should call alethiaService.getOwners with correct data', async () => {
      const mockOwner = {
        devices: [
          {
            target: [
              {
                id: 1234,
                name: 'test.netops.charter.com',
                type: 'router',
              },
            ],
            source: '*********/32',
            destination: '*********/32',
            service: 'tcp:any',
            ruleInfo: {
              source: {
                location: 'STAMPDEV',
                ipAddress: '***********/32',
              },
              destination: {
                location: 'PaceLab',
                hostName: 'caas-3-awx.caas.charterlab.com',
                ipAddress: '*************/32',
                port: '',
              },
              protocol: 'ICMP',
              notes:
                'StampDEVCaasEgressV4-->caas-3-awx.caas.charterlab.com ICMP',
              ipVersion: 'v4',
              valid: true,
              ruleId: 1,
            },
          },
        ],
        topologyData: [
          {
            id: 25471,
            name: 'test.netops.charter.com',
            type: 'router',
          },
        ],
      };

      await controller.createAlethiaRequest(mockOwner);
      expect(mockAlethiaWrapperService.getOwners).toBeCalled();
      expect(mockAlethiaWrapperService.getOwners).toHaveBeenCalledWith(
        mockOwner,
      );
    });
  });
});
