import { Module } from '@nestjs/common';
import { AlethiaWrapperController } from './alethia.wrapper.controller';
import { AlethiaWrapperService } from './alethia.wrapper.service';
import { AlethiaService } from './alethia.service';
import { LoggerModule } from '../loggers/logger.module';

@Module({
  imports: [LoggerModule],
  controllers: [AlethiaWrapperController],
  providers: [AlethiaWrapperService, AlethiaService],
})
export class AlethiaWrapperModule {}
