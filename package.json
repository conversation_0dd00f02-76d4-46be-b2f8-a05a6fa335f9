{"name": "nebula-alethia-management-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/axios": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/mongoose": "^10.0.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.16", "axios": "^1.5.1", "axios-retry": "^4.5.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "jest-mock-extended": "^3.0.5", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.1", "nest-winston": "^1.9.4", "nestjs-pino": "^4.1.0", "pino-noir": "^2.2.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuidv4": "^6.2.13", "winston": "^3.11.0"}, "devDependencies": {"@automock/adapters.nestjs": "^2.1.0", "@automock/jest": "^2.1.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "jest-junit": "^16.0.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "overrides": {"superagent": {"formidable": "^3.5.1"}, "semver": "^7.6.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "reporters": ["default", ["jest-junit", {"outputDirectory": "./coverage", "outputName": "junit.xml"}]], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/alethi<PERSON>/**/*.{js,ts}", "!**/*.spec.ts", "!**/*.test.ts"], "coverageDirectory": "coverage", "testEnvironment": "node"}}