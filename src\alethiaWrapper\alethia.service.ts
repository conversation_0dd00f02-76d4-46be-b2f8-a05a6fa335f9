import axios, { AxiosInstance } from 'axios';
import * as https from 'https';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../loggers/logger.service';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { withResponseErrorHandler } from '../utils/helpers';

@Injectable()
export class AlethiaService {
  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {}

  async getDeviceOwnersFromAlethia(devices, topologyData) {
    this.logger.log('Received request to get device owners');
    this.logger.log('Request payload to get device owners', {
      devices,
      topologyData,
    });
    /**
     * Assign organization owner on the results of the Tufin access request path analysis
     */
    const aletheiaUrl = `${process.env.ALETHIA_BASE_URL}/graphql`;
    const aletheiaPayload = this.createAlethiaPayload(topologyData);
    const token = await this.getAlethiaToken();
    let headers = {
      'Content-Type': 'application/json',
    };
    headers['Authorization'] = `Bearer ${token}`;
    this.logger.log('Calling Alethia service with payload', aletheiaPayload);
    const response = await withResponseErrorHandler(
      axios.post(
        aletheiaUrl,
        { query: aletheiaPayload },
        {
          headers,
        },
      ),
    );
    this.logger.log('Received alethia response', response);
    return response;
  }

  private createAlethiaPayload(topologyData) {
    const hostnameArray = [];
    for (const hostname of topologyData) {
      hostnameArray.push(`{
      hostname:
      { includes: "${hostname.name}" }
      }`);
    }

    const payload = `query
      {
      allFiremonDevices(filter:
      {
      or: [${hostnameArray}]
       })
      {
      nodes
      { contact hostname }
        }
       }`;
    this.logger.log('payload for Alethia', payload);
    return payload;
  }

  async getAlethiaToken() {
    this.logger.log('inside getAlethiaToken');
    const aletheiaTokenUrl = `${process.env.ALETHIA_BASE_URL}/api/token`;
    const aletheiaTokenPayload = {
      username: process.env.ALETHIA_USERNAME,
      password: process.env.ALETHIA_PASSWORD,
    };

    let headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    this.logger.log('Calling Alethia token with payload');
    const response = await withResponseErrorHandler(
      axios.post(aletheiaTokenUrl, aletheiaTokenPayload, {
        headers,
      }),
    );
    this.logger.log('Received alethia token', response.access_token);
    return response.access_token;
  }
}
