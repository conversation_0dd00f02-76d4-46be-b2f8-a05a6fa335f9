import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER } from '@nestjs/core';
import { AppModule } from './app.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AllExceptionsFilter } from './utils/filters/allException.filter';
import { LoggerModule } from './loggers/logger.module';
import { AlethiaWrapperModule } from './alethiaWrapper/alethia.wrapper.module';
import { LoggerMiddleware } from './loggers/logger.middleware';
import { MiddlewareConsumer, NestModule } from '@nestjs/common';

// Mock external modules
jest.mock('./loggers/logger.module', () => ({
  LoggerModule: class MockLoggerModule {}
}));

jest.mock('./alethiaWrapper/alethia.wrapper.module', () => ({
  AlethiaWrapperModule: class MockAlethiaWrapperModule {}
}));

jest.mock('./loggers/logger.middleware', () => ({
  LoggerMiddleware: class MockLoggerMiddleware {}
}));

jest.mock('./utils/filters/allException.filter', () => ({
  AllExceptionsFilter: class MockAllExceptionsFilter {}
}));

describe('AppModule', () => {
  let module: TestingModule;
  let appModule: AppModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    appModule = module.get<AppModule>(AppModule);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe('Module instantiation', () => {
    it('should be defined', () => {
      expect(appModule).toBeDefined();
    });

    it('should be an instance of AppModule', () => {
      expect(appModule).toBeInstanceOf(AppModule);
    });

    it('should implement NestModule', () => {
      expect(appModule).toBeInstanceOf(Object);
      expect(typeof appModule.configure).toBe('function');
    });
  });

  describe('Module configuration', () => {
    it('should have correct module metadata', () => {
      const moduleMetadata = Reflect.getMetadata('imports', AppModule) || [];
      expect(moduleMetadata).toBeDefined();
      expect(Array.isArray(moduleMetadata)).toBe(true);
    });

    it('should import ConfigModule', () => {
      const imports = Reflect.getMetadata('imports', AppModule) || [];
      const hasConfigModule = imports.some((imp: any) => 
        imp === ConfigModule || 
        (imp && imp.module === ConfigModule) ||
        (typeof imp === 'object' && imp.constructor && imp.constructor.name === 'DynamicModule')
      );
      expect(hasConfigModule).toBe(true);
    });

    it('should import LoggerModule', () => {
      const imports = Reflect.getMetadata('imports', AppModule) || [];
      expect(imports).toContain(LoggerModule);
    });

    it('should import AlethiaWrapperModule', () => {
      const imports = Reflect.getMetadata('imports', AppModule) || [];
      expect(imports).toContain(AlethiaWrapperModule);
    });
  });

  describe('Controllers', () => {
    it('should register AppController', () => {
      const controllers = Reflect.getMetadata('controllers', AppModule) || [];
      expect(controllers).toContain(AppController);
    });

    it('should have AppController available in module', () => {
      const appController = module.get<AppController>(AppController);
      expect(appController).toBeDefined();
      expect(appController).toBeInstanceOf(AppController);
    });
  });

  describe('Providers', () => {
    it('should register AppService', () => {
      const providers = Reflect.getMetadata('providers', AppModule) || [];
      const hasAppService = providers.some((provider: any) => 
        provider === AppService || 
        (provider && provider.useClass === AppService)
      );
      expect(hasAppService).toBe(true);
    });

    it('should register AllExceptionsFilter as APP_FILTER', () => {
      const providers = Reflect.getMetadata('providers', AppModule) || [];
      const hasExceptionFilter = providers.some((provider: any) => 
        provider && 
        provider.provide === APP_FILTER && 
        provider.useClass === AllExceptionsFilter
      );
      expect(hasExceptionFilter).toBe(true);
    });

    it('should have AppService available in module', () => {
      const appService = module.get<AppService>(AppService);
      expect(appService).toBeDefined();
      expect(appService).toBeInstanceOf(AppService);
    });
  });

  describe('Exports', () => {
    it('should have empty exports array', () => {
      const exports = Reflect.getMetadata('exports', AppModule) || [];
      expect(exports).toEqual([]);
    });
  });

  describe('Middleware configuration', () => {
    it('should have configure method', () => {
      expect(typeof appModule.configure).toBe('function');
    });

    it('should configure LoggerMiddleware for all routes', () => {
      const mockConsumer = {
        apply: jest.fn().mockReturnThis(),
        forRoutes: jest.fn().mockReturnThis(),
      };

      appModule.configure(mockConsumer as any);

      expect(mockConsumer.apply).toHaveBeenCalledWith(LoggerMiddleware);
      expect(mockConsumer.forRoutes).toHaveBeenCalledWith('*');
    });

    it('should call apply and forRoutes in correct order', () => {
      const mockConsumer = {
        apply: jest.fn().mockReturnThis(),
        forRoutes: jest.fn().mockReturnThis(),
      };

      appModule.configure(mockConsumer as any);

      expect(mockConsumer.apply).toHaveBeenCalledBefore(mockConsumer.forRoutes as jest.Mock);
    });
  });

  describe('ConfigModule validation', () => {
    it('should configure ConfigModule with correct options', async () => {
      // This test verifies the module can be created with the validation schema
      const testModule = await Test.createTestingModule({
        imports: [AppModule],
      })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn((key: string) => {
          const config = {
            PORT: '3000',
            GLOBAL_ROUTE_PREFIX: '/api',
            REQUEST_BODY_SIZE_LIMIT: '5mb'
          };
          return config[key];
        })
      })
      .compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });
  });

  describe('Module dependencies', () => {
    it('should resolve all dependencies successfully', async () => {
      const testModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      // Test that all main components can be resolved
      expect(() => testModule.get<AppController>(AppController)).not.toThrow();
      expect(() => testModule.get<AppService>(AppService)).not.toThrow();
      expect(() => testModule.get<ConfigService>(ConfigService)).not.toThrow();

      await testModule.close();
    });
  });

  describe('Module lifecycle', () => {
    it('should initialize without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      await expect(testModule.init()).resolves.not.toThrow();
      await testModule.close();
    });

    it('should close without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      await testModule.init();
      await expect(testModule.close()).resolves.not.toThrow();
    });
  });

  describe('Integration tests', () => {
    it('should create a complete application context', async () => {
      const testModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      const app = testModule.createNestApplication();
      await expect(app.init()).resolves.not.toThrow();
      
      await app.close();
      await testModule.close();
    });

    it('should have all required services available', async () => {
      const testModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      const appController = testModule.get<AppController>(AppController);
      const appService = testModule.get<AppService>(AppService);
      const configService = testModule.get<ConfigService>(ConfigService);

      expect(appController).toBeDefined();
      expect(appService).toBeDefined();
      expect(configService).toBeDefined();

      await testModule.close();
    });
  });

  describe('Error scenarios', () => {
    it('should handle missing required environment variables gracefully in test environment', async () => {
      // In test environment, we expect the module to still be creatable
      // even if some env vars are missing, as they might be mocked
      const testModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });
  });

  describe('Type safety', () => {
    it('should implement NestModule interface correctly', () => {
      expect(appModule).toHaveProperty('configure');
      expect(typeof appModule.configure).toBe('function');
      
      // Test that configure method accepts MiddlewareConsumer
      const mockConsumer = {
        apply: jest.fn().mockReturnThis(),
        forRoutes: jest.fn().mockReturnThis(),
      } as any as MiddlewareConsumer;

      expect(() => appModule.configure(mockConsumer)).not.toThrow();
    });
  });
});
