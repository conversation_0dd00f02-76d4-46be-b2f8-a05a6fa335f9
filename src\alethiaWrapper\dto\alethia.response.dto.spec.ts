import { RemoveDeviceResponseDto } from './alethia.response.dto';

describe('RemoveDeviceResponseDto', () => {
  it('should create an instance with all properties', () => {
    const dto = new RemoveDeviceResponseDto();
    dto.requestType = 'REMOVE_DEVICE';
    dto.status = 'success';
    dto.error = null;

    expect(dto.requestType).toBe('REMOVE_DEVICE');
    expect(dto.status).toBe('success');
    expect(dto.error).toBeNull();
  });

  it('should allow "error" to be undefined', () => {
    const dto = new RemoveDeviceResponseDto();
    dto.requestType = 'REMOVE_DEVICE';
    dto.status = 'pending';

    expect(dto.error).toBeUndefined();
    expect(dto.status).toBe('pending');
  });
});
