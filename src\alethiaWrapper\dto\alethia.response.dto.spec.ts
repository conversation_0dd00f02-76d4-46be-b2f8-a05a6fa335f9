import { RemoveDeviceResponseDto } from './alethia.response.dto';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

describe('RemoveDeviceResponseDto', () => {
  describe('Class instantiation', () => {
    it('should create an instance of RemoveDeviceResponseDto', () => {
      const dto = new RemoveDeviceResponseDto();
      expect(dto).toBeInstanceOf(RemoveDeviceResponseDto);
    });

    it('should have requestType, status, and error properties', () => {
      const dto = new RemoveDeviceResponseDto();
      expect(dto).toHaveProperty('requestType');
      expect(dto).toHaveProperty('status');
      expect(dto).toHaveProperty('error');
    });
  });

  describe('Property assignment', () => {
    it('should allow setting requestType property', () => {
      const dto = new RemoveDeviceResponseDto();
      const testRequestType = 'REMOVE_DEVICE';
      
      dto.requestType = testRequestType;
      
      expect(dto.requestType).toBe(testRequestType);
    });

    it('should allow setting status property', () => {
      const dto = new RemoveDeviceResponseDto();
      const testStatus = 'SUCCESS';
      
      dto.status = testStatus;
      
      expect(dto.status).toBe(testStatus);
    });

    it('should allow setting error property', () => {
      const dto = new RemoveDeviceResponseDto();
      const testError = { code: 'ERR001', message: 'Test error' };
      
      dto.error = testError;
      
      expect(dto.error).toEqual(testError);
    });

    it('should allow setting all properties', () => {
      const dto = new RemoveDeviceResponseDto();
      const testRequestType = 'REMOVE_DEVICE';
      const testStatus = 'FAILED';
      const testError = { code: 'ERR002', message: 'Device not found' };
      
      dto.requestType = testRequestType;
      dto.status = testStatus;
      dto.error = testError;
      
      expect(dto.requestType).toBe(testRequestType);
      expect(dto.status).toBe(testStatus);
      expect(dto.error).toEqual(testError);
    });
  });

  describe('Object creation with constructor-like pattern', () => {
    it('should create RemoveDeviceResponseDto with Object.assign', () => {
      const data = {
        requestType: 'REMOVE_DEVICE',
        status: 'SUCCESS',
        error: null
      };
      
      const dto = Object.assign(new RemoveDeviceResponseDto(), data);
      
      expect(dto.requestType).toBe(data.requestType);
      expect(dto.status).toBe(data.status);
      expect(dto.error).toBe(data.error);
    });

    it('should create RemoveDeviceResponseDto with spread operator', () => {
      const data = {
        requestType: 'REMOVE_DEVICE',
        status: 'FAILED',
        error: { message: 'Network error' }
      };
      
      const dto = { ...new RemoveDeviceResponseDto(), ...data };
      
      expect(dto.requestType).toBe(data.requestType);
      expect(dto.status).toBe(data.status);
      expect(dto.error).toEqual(data.error);
    });
  });

  describe('Class transformation', () => {
    it('should transform plain object to RemoveDeviceResponseDto instance', () => {
      const plainObject = {
        requestType: 'REMOVE_DEVICE',
        status: 'SUCCESS',
        error: undefined
      };
      
      const dto = plainToClass(RemoveDeviceResponseDto, plainObject);
      
      expect(dto).toBeInstanceOf(RemoveDeviceResponseDto);
      expect(dto.requestType).toBe(plainObject.requestType);
      expect(dto.status).toBe(plainObject.status);
      expect(dto.error).toBe(plainObject.error);
    });

    it('should handle transformation with extra properties', () => {
      const plainObject = {
        requestType: 'REMOVE_DEVICE',
        status: 'SUCCESS',
        error: null,
        extraProperty: 'should be preserved'
      };
      
      const dto = plainToClass(RemoveDeviceResponseDto, plainObject);
      
      expect(dto).toBeInstanceOf(RemoveDeviceResponseDto);
      expect(dto.requestType).toBe(plainObject.requestType);
      expect(dto.status).toBe(plainObject.status);
      expect(dto.error).toBe(plainObject.error);
      expect((dto as any).extraProperty).toBe(plainObject.extraProperty);
    });

    it('should handle transformation with missing optional error property', () => {
      const plainObject = {
        requestType: 'REMOVE_DEVICE',
        status: 'SUCCESS'
      };
      
      const dto = plainToClass(RemoveDeviceResponseDto, plainObject);
      
      expect(dto).toBeInstanceOf(RemoveDeviceResponseDto);
      expect(dto.requestType).toBe(plainObject.requestType);
      expect(dto.status).toBe(plainObject.status);
      expect(dto.error).toBeUndefined();
    });
  });

  describe('Validation', () => {
    it('should validate successfully with valid data', async () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'REMOVE_DEVICE';
      dto.status = 'SUCCESS';
      dto.error = null;
      
      const errors = await validate(dto);
      
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with error object', async () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'REMOVE_DEVICE';
      dto.status = 'FAILED';
      dto.error = { code: 'ERR001', message: 'Test error' };
      
      const errors = await validate(dto);
      
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with undefined error', async () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'REMOVE_DEVICE';
      dto.status = 'SUCCESS';
      // error is undefined by default
      
      const errors = await validate(dto);
      
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with empty strings', async () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = '';
      dto.status = '';
      dto.error = null;
      
      const errors = await validate(dto);
      
      expect(errors).toHaveLength(0);
    });
  });

  describe('JSON serialization', () => {
    it('should serialize to JSON correctly', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'REMOVE_DEVICE';
      dto.status = 'SUCCESS';
      dto.error = null;
      
      const json = JSON.stringify(dto);
      const parsed = JSON.parse(json);
      
      expect(parsed.requestType).toBe(dto.requestType);
      expect(parsed.status).toBe(dto.status);
      expect(parsed.error).toBe(dto.error);
    });

    it('should serialize complex error object correctly', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'REMOVE_DEVICE';
      dto.status = 'FAILED';
      dto.error = {
        code: 'ERR001',
        message: 'Device not found',
        details: { deviceId: '12345', timestamp: '2023-01-01T00:00:00Z' }
      };
      
      const json = JSON.stringify(dto);
      const parsed = JSON.parse(json);
      
      expect(parsed.error).toEqual(dto.error);
      expect(parsed.error.details.deviceId).toBe('12345');
    });

    it('should deserialize from JSON correctly', () => {
      const jsonString = '{"requestType":"REMOVE_DEVICE","status":"SUCCESS","error":null}';
      const parsed = JSON.parse(jsonString);
      const dto = Object.assign(new RemoveDeviceResponseDto(), parsed);
      
      expect(dto).toBeInstanceOf(RemoveDeviceResponseDto);
      expect(dto.requestType).toBe('REMOVE_DEVICE');
      expect(dto.status).toBe('SUCCESS');
      expect(dto.error).toBeNull();
    });
  });

  describe('Edge cases', () => {
    it('should handle null values', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = null as any;
      dto.status = null as any;
      dto.error = null;
      
      expect(dto.requestType).toBeNull();
      expect(dto.status).toBeNull();
      expect(dto.error).toBeNull();
    });

    it('should handle undefined values', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = undefined as any;
      dto.status = undefined as any;
      dto.error = undefined;
      
      expect(dto.requestType).toBeUndefined();
      expect(dto.status).toBeUndefined();
      expect(dto.error).toBeUndefined();
    });

    it('should handle numeric values as strings', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = '123' as string;
      dto.status = '456' as string;
      dto.error = 789 as any;
      
      expect(dto.requestType).toBe('123');
      expect(dto.status).toBe('456');
      expect(dto.error).toBe(789);
    });

    it('should handle special characters in strings', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'REMOVE_DEVICE_@#$%';
      dto.status = 'SUCCESS_WITH_WARNINGS!';
      dto.error = { message: 'Error with special chars: <>?/\\' };
      
      expect(dto.requestType).toBe('REMOVE_DEVICE_@#$%');
      expect(dto.status).toBe('SUCCESS_WITH_WARNINGS!');
      expect(dto.error.message).toBe('Error with special chars: <>?/\\');
    });

    it('should handle very long strings', () => {
      const dto = new RemoveDeviceResponseDto();
      const longRequestType = 'R'.repeat(1000);
      const longStatus = 'S'.repeat(1000);
      const longError = { message: 'E'.repeat(1000) };
      
      dto.requestType = longRequestType;
      dto.status = longStatus;
      dto.error = longError;
      
      expect(dto.requestType).toBe(longRequestType);
      expect(dto.status).toBe(longStatus);
      expect(dto.error).toEqual(longError);
    });
  });

  describe('Type checking', () => {
    it('should have string type for requestType property', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'test';
      
      expect(typeof dto.requestType).toBe('string');
    });

    it('should have string type for status property', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.status = 'test';
      
      expect(typeof dto.status).toBe('string');
    });

    it('should allow any type for error property', () => {
      const dto = new RemoveDeviceResponseDto();
      
      dto.error = 'string error';
      expect(typeof dto.error).toBe('string');
      
      dto.error = { code: 'ERR001' };
      expect(typeof dto.error).toBe('object');
      
      dto.error = 123;
      expect(typeof dto.error).toBe('number');
      
      dto.error = null;
      expect(dto.error).toBeNull();
    });
  });

  describe('Real-world usage scenarios', () => {
    it('should work with typical success response', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'REMOVE_DEVICE';
      dto.status = 'SUCCESS';
      dto.error = null;
      
      expect(dto.requestType).toBe('REMOVE_DEVICE');
      expect(dto.status).toBe('SUCCESS');
      expect(dto.error).toBeNull();
    });

    it('should work with typical error response', () => {
      const dto = new RemoveDeviceResponseDto();
      dto.requestType = 'REMOVE_DEVICE';
      dto.status = 'FAILED';
      dto.error = {
        code: 'DEVICE_NOT_FOUND',
        message: 'The specified device could not be found',
        timestamp: new Date().toISOString()
      };
      
      expect(dto.status).toBe('FAILED');
      expect(dto.error.code).toBe('DEVICE_NOT_FOUND');
      expect(dto.error.message).toContain('device could not be found');
    });

    it('should work with different request types', () => {
      const requestTypes = ['REMOVE_DEVICE', 'ADD_DEVICE', 'UPDATE_DEVICE', 'GET_DEVICE'];
      
      requestTypes.forEach(requestType => {
        const dto = new RemoveDeviceResponseDto();
        dto.requestType = requestType;
        dto.status = 'SUCCESS';
        
        expect(dto.requestType).toBe(requestType);
      });
    });

    it('should work with different status values', () => {
      const statuses = ['SUCCESS', 'FAILED', 'PENDING', 'IN_PROGRESS', 'CANCELLED'];
      
      statuses.forEach(status => {
        const dto = new RemoveDeviceResponseDto();
        dto.requestType = 'REMOVE_DEVICE';
        dto.status = status;
        
        expect(dto.status).toBe(status);
      });
    });
  });
});
