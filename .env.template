
#Port to run the application #REQUIRED 
PORT=8088

#Global routing prefix for this application #REQUIRED 
GLOBAL_ROUTE_PREFIX=nebula-alethia-management-service
LOG_LEVEL=trace

ALETHIA_USERNAME=
ALETHIA_PASSWORD=

ALETHIA_BASE_URL= "https://aletheia-api.netops.charter.com"


NODE_TLS_REJECT_UNAUTHORIZED="0"
LOG_LEVEL="trace"

REQUEST_BODY_SIZE_LIMIT= "5mb"


BLUE_SOT_URL="https://142.136.34.105/simplapi/device_lookup"
PERFORM_REVERSE_PATH_ANALYSIS= "true"
PINXT_TOKEN_URL="https://apis-internal-beta-corp.shared.dev-spectrum.net"
ALETHEIA_REST_URL="https://aletheia.netops.charter.com/api/v1"
BLUE_SOT_API_MAX_RETRY_COUNT=3
ENABLE_BLUE_SOT_RETRY=true