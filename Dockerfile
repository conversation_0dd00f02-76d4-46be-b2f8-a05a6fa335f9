FROM artifactory.charterlab.com/reliability-engineering-docker-release-ctec-stamp/base-images/node:16-alpine

ARG ARTIFACTORY_SERVICE_ACCOUNT=pass_this_as_build_arg
ARG ARTIFACTORY_SERVICE_CREDENTIALS=pass_this_as_build_arg
ARG ARTIFACTORY_SERVICE_URL=pass_this_as_build_arg
ARG ARTIFACTORY_NPM_REGISTRY_NAME=pass_this_as_build_arg

RUN sed -ie "s#https://dl-cdn.alpinelinux.org#http://dl-cdn.alpinelinux.org#g" /etc/apk/repositories

RUN apk add --no-cache --upgrade grep
RUN apk add --no-cache curl

WORKDIR /opt/app
COPY . /opt/app
RUN pwd && ls


RUN sed --in-place='.orig' "s#https://registry.npmjs.org/#https://${ARTIFACTORY_SERVICE_URL}/artifactory/api/npm/${ARTIFACTORY_NPM_REGISTRY_NAME}/#g" package-lock.json

RUN echo "registry=https://${ARTIFACTORY_SERVICE_URL}/artifactory/api/npm/${ARTIFACTORY_NPM_REGISTRY_NAME}" > ~/.npmrc

RUN echo "`curl -u $ARTIFACTORY_SERVICE_ACCOUNT:$(echo $ARTIFACTORY_SERVICE_CREDENTIALS | base64 -d) https://$ARTIFACTORY_SERVICE_URL/artifactory/api/npm/auth`" >> ~/.npmrc

RUN npm install --loglevel verbose

RUN npm run build

CMD ["npm", "start"]

EXPOSE 8088
