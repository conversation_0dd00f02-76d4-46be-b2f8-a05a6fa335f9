import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsDefined,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export const ORGANIZATION = {
  CORPORATE: 'corporate',
  RED_APS: 'red_aps',
  RED_CBO: 'red_cbo',
  UNKNOWN: 'unknown',
};

export const CONTACT = {
  INF: 'inf.charter.com',
  APS: 'aps',
  DC: 'datacenter',
  CBO: 'cbo',
};

export class Target {
  id: number;
  name: string;
  owner?: string;
  organization?: string;
}

export class Host {
  @IsString()
  @IsNotEmpty()
  @IsDefined()
  hostName: string;

  @IsString()
  @IsOptional()
  location: string;

  @IsString()
  @IsNotEmpty()
  @IsDefined()
  ipAddress: string;

  @IsString()
  @IsNotEmpty()
  @IsDefined()
  port: string;
}

export class FirewallRule {
  @ValidateNested()
  @Type(() => Host)
  @IsObject({ each: true })
  @IsDefined()
  source: Host;

  @ValidateNested()
  @Type(() => Host)
  @IsObject({ each: true })
  @IsDefined()
  destination: Host;

  @IsString()
  @IsOptional()
  protocol: string;

  @IsString()
  @IsOptional()
  notes?: string;

  @IsString()
  @IsOptional()
  ruleId?: number;
}

export class DeviceOwnerRequestDto {
  target: Target[];
  source: string;
  destination: string;
  service?: string;
  ruleInfo?: FirewallRule;
}

export class AlethiaRequestDto {
  @ApiProperty()
  devices: DeviceRequestDto[];

  @ApiProperty()
  topologyData: targetDto[];;
}

export class targetDto {
  id: number;
  name: string;
  type?: string;
}

export class DeviceRequestDto {
  target: targetDto[];
  source?: string;
  destination?: string;
  service?: string;
  ruleInfo?: FirewallRule;
}
