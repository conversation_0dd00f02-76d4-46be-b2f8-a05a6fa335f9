import { plainToClass } from 'class-transformer';
import {
  AlethiaRequestDto,
  DeviceRequestDto,
  targetDto,
  Host,
  FirewallRule,
  DeviceOwnerRequestDto,
  ORGANIZATION,
  CONTACT,
} from './alethia.request.dto';

describe('AlethiaRequestDto', () => {
  it('should create an instance', () => {
    const dto = new AlethiaRequestDto();
    expect(dto).toBeDefined();
  });

  it('should transform data correctly', () => {
    const requestData = {
      devices: [
        {
          target: [
            {
              id: 1,
              name: 'test.example.com',
              type: 'router',
            },
          ],
          source: '***********',
          destination: '***********',
          service: 'tcp:80',
        },
      ],
      topologyData: [
        {
          id: 1,
          name: 'test.example.com',
          type: 'router',
        },
      ],
    };

    const dto = plainToClass(AlethiaRequestDto, requestData);
    expect(dto.devices).toBeDefined();
    expect(dto.topologyData).toBeDefined();
    expect(dto.devices.length).toBe(1);
    expect(dto.topologyData.length).toBe(1);
  });
});

describe('DeviceRequestDto', () => {
  it('should create an instance', () => {
    const dto = new DeviceRequestDto();
    expect(dto).toBeDefined();
  });

  it('should transform device request data', () => {
    const deviceData = {
      target: [
        {
          id: 1,
          name: 'test.example.com',
          type: 'router',
        },
      ],
      source: '***********',
      destination: '***********',
      service: 'tcp:80',
    };

    const dto = plainToClass(DeviceRequestDto, deviceData);
    expect(dto.target).toBeDefined();
    expect(dto.source).toBe('***********');
    expect(dto.destination).toBe('***********');
    expect(dto.service).toBe('tcp:80');
  });
});

describe('targetDto', () => {
  it('should create an instance', () => {
    const dto = new targetDto();
    expect(dto).toBeDefined();
  });

  it('should transform target data', () => {
    const targetData = {
      id: 1,
      name: 'test.example.com',
      type: 'router',
    };

    const dto = plainToClass(targetDto, targetData);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('test.example.com');
    expect(dto.type).toBe('router');
  });
});

describe('Host', () => {
  it('should create an instance', () => {
    const dto = new Host();
    expect(dto).toBeDefined();
  });

  it('should transform host data', () => {
    const hostData = {
      hostName: 'test.example.com',
      location: 'DataCenter1',
      ipAddress: '***********',
      port: '80',
    };

    const dto = plainToClass(Host, hostData);
    expect(dto.hostName).toBe('test.example.com');
    expect(dto.location).toBe('DataCenter1');
    expect(dto.ipAddress).toBe('***********');
    expect(dto.port).toBe('80');
  });
});

describe('FirewallRule', () => {
  it('should create an instance', () => {
    const dto = new FirewallRule();
    expect(dto).toBeDefined();
  });

  it('should validate firewall rule with Host transformation', async () => {
    const firewallData = {
      source: {
        hostName: 'source.example.com',
        location: 'DataCenter1',
        ipAddress: '***********',
        port: '80',
      },
      destination: {
        hostName: 'dest.example.com',
        location: 'DataCenter2',
        ipAddress: '***********',
        port: '443',
      },
      protocol: 'TCP',
      notes: 'Test firewall rule',
      ipVersion: 'v4',
      valid: true,
      ruleId: 1,
    };

    // This will test the @Type(() => Host) decorators on lines 57 and 63
    const dto = plainToClass(FirewallRule, firewallData);

    // Test that transformation worked correctly
    expect(dto.source).toBeInstanceOf(Host);
    expect(dto.destination).toBeInstanceOf(Host);
    expect(dto.source.hostName).toBe('source.example.com');
    expect(dto.destination.hostName).toBe('dest.example.com');
  });

  it('should handle transformation of different data types', () => {
    const invalidData = {
      source: 'invalid-source', // Should be Host object
      destination: 'invalid-destination', // Should be Host object
    };

    const dto = plainToClass(FirewallRule, invalidData);

    // Test that the transformation still works even with invalid data
    expect(dto).toBeInstanceOf(FirewallRule);
    expect(dto.source).toBe('invalid-source');
    expect(dto.destination).toBe('invalid-destination');
  });
});

describe('DeviceOwnerRequestDto', () => {
  it('should create an instance', () => {
    const dto = new DeviceOwnerRequestDto();
    expect(dto).toBeDefined();
  });

  it('should transform device owner request data', () => {
    const ownerData = {
      target: [
        {
          id: 1,
          name: 'test.example.com',
          type: 'router',
          owner: 'test-owner',
          organization: 'red_aps',
        },
      ],
      source: '***********',
      destination: '***********',
      service: 'tcp:80',
    };

    const dto = plainToClass(DeviceOwnerRequestDto, ownerData);
    expect(dto.target).toBeDefined();
    expect(dto.target.length).toBe(1);
    expect(dto.target[0].owner).toBe('test-owner');
    expect(dto.target[0].organization).toBe('red_aps');
  });
});

describe('Constants', () => {
  it('should have correct ORGANIZATION values', () => {
    expect(ORGANIZATION.CORPORATE).toBe('corporate');
    expect(ORGANIZATION.RED_APS).toBe('red_aps');
    expect(ORGANIZATION.RED_CBO).toBe('red_cbo');
    expect(ORGANIZATION.UNKNOWN).toBe('unknown');
  });

  it('should have correct CONTACT values', () => {
    expect(CONTACT.INF).toBe('inf.charter.com');
    expect(CONTACT.APS).toBe('aps');
    expect(CONTACT.DC).toBe('datacenter');
    expect(CONTACT.CBO).toBe('cbo');
  });
});
