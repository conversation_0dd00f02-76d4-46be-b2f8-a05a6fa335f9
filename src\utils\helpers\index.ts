import { AxiosResponse } from 'axios';
import { HttpException } from '@nestjs/common';

export async function withResponseErrorHandler(
  axiosPromise: Promise<AxiosResponse>,
) {
  try {
    return (await axiosPromise).data;
  } catch (error) {
    if (error.response) {
      throw new HttpException(error.response.data, error.response.status, {
        cause: error,
      });
    }
    throw error;
  }
}
