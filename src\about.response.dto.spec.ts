import { AboutDto } from './about.response.dto';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

describe('AboutDto', () => {
  describe('Class instantiation', () => {
    it('should create an instance of AboutDto', () => {
      const aboutDto = new AboutDto();
      expect(aboutDto).toBeInstanceOf(AboutDto);
    });

    it('should have version and serviceName properties defined', () => {
      const aboutDto = new AboutDto();
      aboutDto.version = 'test';
      aboutDto.serviceName = 'test';
      expect(aboutDto).toHaveProperty('version');
      expect(aboutDto).toHaveProperty('serviceName');
    });
  });

  describe('Property assignment', () => {
    it('should allow setting version property', () => {
      const aboutDto = new AboutDto();
      const testVersion = '1.0.0';
      
      aboutDto.version = testVersion;
      
      expect(aboutDto.version).toBe(testVersion);
    });

    it('should allow setting serviceName property', () => {
      const aboutDto = new AboutDto();
      const testServiceName = 'nebula-alethia-mgmt-service';
      
      aboutDto.serviceName = testServiceName;
      
      expect(aboutDto.serviceName).toBe(testServiceName);
    });

    it('should allow setting both properties', () => {
      const aboutDto = new AboutDto();
      const testVersion = '2.1.0';
      const testServiceName = 'test-service';
      
      aboutDto.version = testVersion;
      aboutDto.serviceName = testServiceName;
      
      expect(aboutDto.version).toBe(testVersion);
      expect(aboutDto.serviceName).toBe(testServiceName);
    });
  });

  describe('Object creation with constructor-like pattern', () => {
    it('should create AboutDto with Object.assign', () => {
      const data = {
        version: '1.5.0',
        serviceName: 'my-service'
      };
      
      const aboutDto = Object.assign(new AboutDto(), data);
      
      expect(aboutDto.version).toBe(data.version);
      expect(aboutDto.serviceName).toBe(data.serviceName);
    });

    it('should create AboutDto with spread operator', () => {
      const data = {
        version: '3.0.0',
        serviceName: 'another-service'
      };
      
      const aboutDto = { ...new AboutDto(), ...data };
      
      expect(aboutDto.version).toBe(data.version);
      expect(aboutDto.serviceName).toBe(data.serviceName);
    });
  });

  describe('Class transformation', () => {
    it('should transform plain object to AboutDto instance', () => {
      const plainObject = {
        version: '1.0.0',
        serviceName: 'nebula-alethia-mgmt-service'
      };
      
      const aboutDto = plainToClass(AboutDto, plainObject);
      
      expect(aboutDto).toBeInstanceOf(AboutDto);
      expect(aboutDto.version).toBe(plainObject.version);
      expect(aboutDto.serviceName).toBe(plainObject.serviceName);
    });

    it('should handle transformation with extra properties', () => {
      const plainObject = {
        version: '1.0.0',
        serviceName: 'nebula-alethia-mgmt-service',
        extraProperty: 'should be ignored'
      };
      
      const aboutDto = plainToClass(AboutDto, plainObject);
      
      expect(aboutDto).toBeInstanceOf(AboutDto);
      expect(aboutDto.version).toBe(plainObject.version);
      expect(aboutDto.serviceName).toBe(plainObject.serviceName);
      expect((aboutDto as any).extraProperty).toBe(plainObject.extraProperty);
    });
  });

  describe('Validation', () => {
    it('should validate successfully with valid data', async () => {
      const aboutDto = new AboutDto();
      aboutDto.version = '1.0.0';
      aboutDto.serviceName = 'nebula-alethia-mgmt-service';
      
      const errors = await validate(aboutDto);
      
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with empty strings', async () => {
      const aboutDto = new AboutDto();
      aboutDto.version = '';
      aboutDto.serviceName = '';
      
      const errors = await validate(aboutDto);
      
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with undefined values', async () => {
      const aboutDto = new AboutDto();
      // Properties are undefined by default
      
      const errors = await validate(aboutDto);
      
      expect(errors).toHaveLength(0);
    });
  });

  describe('JSON serialization', () => {
    it('should serialize to JSON correctly', () => {
      const aboutDto = new AboutDto();
      aboutDto.version = '1.0.0';
      aboutDto.serviceName = 'nebula-alethia-mgmt-service';
      
      const json = JSON.stringify(aboutDto);
      const parsed = JSON.parse(json);
      
      expect(parsed.version).toBe(aboutDto.version);
      expect(parsed.serviceName).toBe(aboutDto.serviceName);
    });

    it('should deserialize from JSON correctly', () => {
      const jsonString = '{"version":"2.0.0","serviceName":"test-service"}';
      const parsed = JSON.parse(jsonString);
      const aboutDto = Object.assign(new AboutDto(), parsed);
      
      expect(aboutDto).toBeInstanceOf(AboutDto);
      expect(aboutDto.version).toBe('2.0.0');
      expect(aboutDto.serviceName).toBe('test-service');
    });
  });

  describe('Edge cases', () => {
    it('should handle null values', () => {
      const aboutDto = new AboutDto();
      aboutDto.version = null as any;
      aboutDto.serviceName = null as any;
      
      expect(aboutDto.version).toBeNull();
      expect(aboutDto.serviceName).toBeNull();
    });

    it('should handle numeric values as strings', () => {
      const aboutDto = new AboutDto();
      aboutDto.version = '123' as string;
      aboutDto.serviceName = '456' as string;
      
      expect(aboutDto.version).toBe('123');
      expect(aboutDto.serviceName).toBe('456');
    });

    it('should handle special characters in strings', () => {
      const aboutDto = new AboutDto();
      aboutDto.version = '1.0.0-beta+build.123';
      aboutDto.serviceName = 'service-with-special-chars_123';
      
      expect(aboutDto.version).toBe('1.0.0-beta+build.123');
      expect(aboutDto.serviceName).toBe('service-with-special-chars_123');
    });

    it('should handle very long strings', () => {
      const aboutDto = new AboutDto();
      const longVersion = 'v'.repeat(1000);
      const longServiceName = 's'.repeat(1000);
      
      aboutDto.version = longVersion;
      aboutDto.serviceName = longServiceName;
      
      expect(aboutDto.version).toBe(longVersion);
      expect(aboutDto.serviceName).toBe(longServiceName);
    });
  });

  describe('Type checking', () => {
    it('should have string type for version property', () => {
      const aboutDto = new AboutDto();
      aboutDto.version = 'test';
      
      expect(typeof aboutDto.version).toBe('string');
    });

    it('should have string type for serviceName property', () => {
      const aboutDto = new AboutDto();
      aboutDto.serviceName = 'test';
      
      expect(typeof aboutDto.serviceName).toBe('string');
    });
  });

  describe('Immutability tests', () => {
    it('should allow property modification', () => {
      const aboutDto = new AboutDto();
      aboutDto.version = '1.0.0';
      aboutDto.serviceName = 'initial-service';
      
      // Modify properties
      aboutDto.version = '2.0.0';
      aboutDto.serviceName = 'updated-service';
      
      expect(aboutDto.version).toBe('2.0.0');
      expect(aboutDto.serviceName).toBe('updated-service');
    });
  });

  describe('Real-world usage scenarios', () => {
    it('should work with typical service information', () => {
      const aboutDto = new AboutDto();
      aboutDto.version = '1.0.0';
      aboutDto.serviceName = 'nebula-alethia-mgmt-service';
      
      expect(aboutDto.version).toMatch(/^\d+\.\d+\.\d+$/);
      expect(aboutDto.serviceName).toContain('nebula');
      expect(aboutDto.serviceName).toContain('alethia');
      expect(aboutDto.serviceName).toContain('service');
    });

    it('should work with semantic versioning patterns', () => {
      const versions = ['1.0.0', '2.1.3', '10.15.20', '0.0.1'];
      
      versions.forEach(version => {
        const aboutDto = new AboutDto();
        aboutDto.version = version;
        aboutDto.serviceName = 'test-service';
        
        expect(aboutDto.version).toBe(version);
        expect(aboutDto.version).toMatch(/^\d+\.\d+\.\d+$/);
      });
    });
  });
});
