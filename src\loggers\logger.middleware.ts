import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { Logger } from 'nestjs-pino';
import { randomUUID } from 'crypto';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  constructor(private readonly logger: Logger) {}

  use(req: Request, res: Response, next: NextFunction) {
    // (req as any).headers = {
    //   ['X-Correlation-ID']:
    //     (req as any).headers['x-correlation-id'] ?? randomUUID(),
    // };

    next();
  }
}
