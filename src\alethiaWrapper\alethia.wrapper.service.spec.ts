import { Test, TestingModule } from '@nestjs/testing';
import { AlethiaWrapperService } from './alethia.wrapper.service';
import { AlethiaService } from './alethia.service';
import { ConfigService } from '@nestjs/config';

import { LoggerService } from '../loggers/logger.service';

type MockAlethiaService = Partial<Record<keyof AlethiaService, jest.Mock>>;

describe('AlethiaWrapperService', () => {
  let service: AlethiaWrapperService;
  let alethiaService: AlethiaService;

  let mockAlethiaService: MockAlethiaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AlethiaService,
        ConfigService,
        LoggerService,
        AlethiaWrapperService,
        {
          provide: AlethiaService,
          useFactory: () => {
            const mockAlethiaService: MockAlethiaService = {
              getDeviceOwnersFromAlethia: jest.fn(),
            };
            return mockAlethiaService;
          },
        },
      ],
    }).compile();

    service = module.get<AlethiaWrapperService>(AlethiaWrapperService);
    alethiaService = module.get<AlethiaService>(AlethiaService);
    mockAlethiaService = module.get<MockAlethiaService>(AlethiaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should be defined', () => {
    expect(alethiaService).toBeDefined();
  });

  describe('get owners', () => {
    it('should return owner detail from alethia service', async () => {
      const mockOwner = {
        devices: [
          {
            target: [
              {
                id: 1234,
                name: 'test.netops.charter.com',
                type: 'router',
              },
            ],
            source: '*********/32',
            destination: '*********/32',
            service: 'tcp:any',
            ruleInfo: {
              source: {
                hostName: 'caas-3-awx.caas.charterlab.com',
                location: 'STAMPDEV',
                ipAddress: '***********/32',
                port: '',
              },
              destination: {
                location: 'PaceLab',
                hostName: 'caas-3-awx.caas.charterlab.com',
                ipAddress: '*************/32',
                port: '',
              },
              protocol: 'ICMP',
              notes:
                'StampDEVCaasEgressV4-->caas-3-awx.caas.charterlab.com ICMP',
              ipVersion: 'v4',
              valid: true,
              ruleId: 1,
            },
          },
        ],
        topologyData: [
          {
            id: 25471,
            name: 'test.netops.charter.com',
            type: 'router',
          },
        ],
      };
      await service.getOwners(mockOwner);
      expect(mockAlethiaService.getDeviceOwnersFromAlethia).toHaveBeenCalled();
      expect(
        mockAlethiaService.getDeviceOwnersFromAlethia,
      ).toHaveBeenCalledWith(mockOwner.devices, mockOwner.topologyData);
    });
  });
});
