import { Test, TestingModule } from '@nestjs/testing';
import { AlethiaWrapperService } from './alethia.wrapper.service';
import { AlethiaService } from './alethia.service';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../loggers/logger.service';
import { withResponseErrorHandler } from '../utils/helpers';
import axios from 'axios';

// Mock axios and helpers
jest.mock('axios');
jest.mock('../utils/helpers', () => ({
  withResponseErrorHandler: jest.fn()
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedWithResponseErrorHandler = withResponseErrorHandler as jest.MockedFunction<typeof withResponseErrorHandler>;

type MockAlethiaService = Partial<Record<keyof AlethiaService, jest.Mock>>;

describe('AlethiaWrapperService', () => {
  let service: AlethiaWrapperService;
  let alethiaService: AlethiaService;
  let mockAlethiaService: MockAlethiaService;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockLoggerService: jest.Mocked<LoggerService>;

  beforeEach(async () => {
    mockConfigService = {
      get: jest.fn()
    } as any;

    mockLoggerService = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AlethiaWrapperService,
        {
          provide: AlethiaService,
          useFactory: () => {
            const mockAlethiaService: MockAlethiaService = {
              getDeviceOwnersFromAlethia: jest.fn(),
            };
            return mockAlethiaService;
          },
        },
        {
          provide: ConfigService,
          useValue: mockConfigService
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService
        }
      ],
    }).compile();

    service = module.get<AlethiaWrapperService>(AlethiaWrapperService);
    alethiaService = module.get<AlethiaService>(AlethiaService);
    mockAlethiaService = module.get<MockAlethiaService>(AlethiaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should be defined', () => {
    expect(alethiaService).toBeDefined();
  });

  describe('get owners', () => {
    it('should return owner detail from alethia service', async () => {
      const mockOwner = {
        devices: [
          {
            target: [
              {
                id: 1234,
                name: 'test.netops.charter.com',
                type: 'router',
              },
            ],
            source: '*********/32',
            destination: '*********/32',
            service: 'tcp:any',
            ruleInfo: {
              source: {
                hostName: 'caas-3-awx.caas.charterlab.com',
                location: 'STAMPDEV',
                ipAddress: '***********/32',
                port: '',
              },
              destination: {
                location: 'PaceLab',
                hostName: 'caas-3-awx.caas.charterlab.com',
                ipAddress: '*************/32',
                port: '',
              },
              protocol: 'ICMP',
              notes:
                'StampDEVCaasEgressV4-->caas-3-awx.caas.charterlab.com ICMP',
              ipVersion: 'v4',
              valid: true,
              ruleId: 1,
            },
          },
        ],
        topologyData: [
          {
            id: 25471,
            name: 'test.netops.charter.com',
            type: 'router',
          },
        ],
      };
      await service.getOwners(mockOwner);
      expect(mockAlethiaService.getDeviceOwnersFromAlethia).toHaveBeenCalled();
      expect(
        mockAlethiaService.getDeviceOwnersFromAlethia,
      ).toHaveBeenCalledWith(mockOwner.devices, mockOwner.topologyData);
    });
  });

  describe('checkForBlue method coverage', () => {
    it('should handle blue SOT success response (line 128)', async () => {
      // Set up environment
      process.env.BLUE_SOT_URL = 'https://test-blue-sot.com';
      mockConfigService.get.mockReturnValue(true);

      const mockBlueResponse = {
        status: 'SUCCESS',
        cmdb: [{ company: 'blue corp', supportedbyteam: 'blue-team' }]
      };

      // Mock axios create and withResponseErrorHandler
      const mockAxiosInstance = { get: jest.fn() };
      mockedAxios.create.mockReturnValue(mockAxiosInstance as any);
      mockedWithResponseErrorHandler.mockResolvedValue(mockBlueResponse);

      const devices = [{ target: [{ name: 'test-host.example.com', owner: 'owner not found' }] }];
      const response = { data: { allFiremonDevices: { nodes: [] } } };

      await service.identifyOrganization(devices, response);

      // Verify line 128: this.logger.log('blue', blue);
      expect(mockLoggerService.log).toHaveBeenCalledWith('blue', mockBlueResponse);
    });

    it('should identify by blue SOT and update owner (lines 137-141)', async () => {
      process.env.BLUE_SOT_URL = 'https://test-blue-sot.com';
      mockConfigService.get.mockReturnValue(true);

      const mockBlueResponse = {
        status: 'SUCCESS',
        cmdb: [{ company: 'blue corp', supportedbyteam: 'blue-support-team' }]
      };

      const mockAxiosInstance = { get: jest.fn() };
      mockedAxios.create.mockReturnValue(mockAxiosInstance as any);
      mockedWithResponseErrorHandler.mockResolvedValue(mockBlueResponse);

      const workflow = { name: 'test-host.example.com', owner: 'owner not found' };
      const devices = [{ target: [workflow] }];
      const response = { data: { allFiremonDevices: { nodes: [] } } };

      await service.identifyOrganization(devices, response);

      // Verify line 137: logger message for blue SOT identification
      expect(mockLoggerService.log).toHaveBeenCalledWith('test-host is identified by blue sot');

      // Verify lines 139-140: owner assignment when owner not found
      expect(workflow.owner).toBe('blue-support-team');
    });

    it('should identify by alethia when owner contains inf (lines 143-144)', async () => {
      process.env.BLUE_SOT_URL = 'https://test-blue-sot.com';
      mockConfigService.get.mockReturnValue(true);

      // Mock blue SOT to return non-blue company
      const mockBlueResponse = {
        status: 'SUCCESS',
        cmdb: [{ company: 'other corp' }]
      };

      const mockAxiosInstance = { get: jest.fn() };
      mockedAxios.create.mockReturnValue(mockAxiosInstance as any);
      mockedWithResponseErrorHandler.mockResolvedValue(mockBlueResponse);

      const workflow = { name: 'test-host.example.com', owner: '<EMAIL>' };
      const devices = [{ target: [workflow] }];
      const response = { data: { allFiremonDevices: { nodes: [] } } };

      await service.identifyOrganization(devices, response);

      // Verify lines 143-144: logger message for alethia identification
      expect(mockLoggerService.log).toHaveBeenCalledWith('test-host is identified by alethia');
    });
  });
});
