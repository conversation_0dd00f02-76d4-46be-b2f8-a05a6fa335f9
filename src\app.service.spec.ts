import { Test, TestingModule } from '@nestjs/testing';
import { AppService } from './app.service';
import { AboutDto } from './about.response.dto';
import * as about from './about.json';

// Mock the about.json import
jest.mock('./about.json', () => ({
  version: '1.0.0',
  serviceName: 'nebula-alethia-mgmt-service'
}));

describe('AppService', () => {
  let service: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AppService],
    }).compile();

    service = module.get<AppService>(AppService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Service instantiation', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should be an instance of AppService', () => {
      expect(service).toBeInstanceOf(AppService);
    });

    it('should have Injectable decorator', () => {
      const injectable = Reflect.getMetadata('__injectable__', AppService);
      expect(injectable).toBe(true);
    });
  });

  describe('getServiceDetails', () => {
    it('should return service details from about.json', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      expect(result).toEqual(about);
      expect(result).toEqual({
        version: '1.0.0',
        serviceName: 'nebula-alethia-mgmt-service'
      });
    });

    it('should return AboutDto type', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('serviceName');
      expect(typeof result.version).toBe('string');
      expect(typeof result.serviceName).toBe('string');
    });

    it('should return a Promise', () => {
      // Act
      const result = service.getServiceDetails();

      // Assert
      expect(result).toBeInstanceOf(Promise);
    });

    it('should resolve to the same data on multiple calls', async () => {
      // Act
      const result1 = await service.getServiceDetails();
      const result2 = await service.getServiceDetails();
      const result3 = await service.getServiceDetails();

      // Assert
      expect(result1).toEqual(result2);
      expect(result2).toEqual(result3);
      expect(result1).toEqual(about);
    });

    it('should return data with correct version format', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      expect(result.version).toMatch(/^\d+\.\d+\.\d+$/);
    });

    it('should return data with correct service name', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      expect(result.serviceName).toBe('nebula-alethia-mgmt-service');
      expect(result.serviceName).toContain('nebula');
      expect(result.serviceName).toContain('alethia');
      expect(result.serviceName).toContain('service');
    });

    it('should handle concurrent calls correctly', async () => {
      // Act
      const promises = Array(10).fill(null).map(() => service.getServiceDetails());
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toEqual(about);
      });
    });

    it('should return the exact same object reference from about.json', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      expect(result).toBe(about);
    });
  });

  describe('Method characteristics', () => {
    it('should be an async method', () => {
      const method = service.getServiceDetails;
      expect(method.constructor.name).toBe('AsyncFunction');
    });

    it('should have correct method signature', () => {
      expect(typeof service.getServiceDetails).toBe('function');
      expect(service.getServiceDetails.length).toBe(0); // No parameters
    });
  });

  describe('Integration with AboutDto', () => {
    it('should return data compatible with AboutDto interface', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      const aboutDto = Object.assign(new AboutDto(), result);
      expect(aboutDto).toBeInstanceOf(AboutDto);
      expect(aboutDto.version).toBe(result.version);
      expect(aboutDto.serviceName).toBe(result.serviceName);
    });

    it('should return data that can be serialized to JSON', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      const jsonString = JSON.stringify(result);
      const parsed = JSON.parse(jsonString);
      expect(parsed).toEqual(result);
    });
  });

  describe('Performance characteristics', () => {
    it('should resolve quickly', async () => {
      // Arrange
      const startTime = Date.now();

      // Act
      await service.getServiceDetails();
      const endTime = Date.now();

      // Assert
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(100); // Should complete in less than 100ms
    });

    it('should handle rapid successive calls', async () => {
      // Arrange
      const startTime = Date.now();

      // Act
      const promises = Array(100).fill(null).map(() => service.getServiceDetails());
      await Promise.all(promises);
      const endTime = Date.now();

      // Assert
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(1000); // Should complete 100 calls in less than 1 second
    });
  });

  describe('Error scenarios', () => {
    it('should not throw any errors under normal conditions', async () => {
      // Act & Assert
      await expect(service.getServiceDetails()).resolves.not.toThrow();
    });

    it('should maintain consistent behavior across multiple instantiations', async () => {
      // Arrange
      const module2: TestingModule = await Test.createTestingModule({
        providers: [AppService],
      }).compile();
      const service2 = module2.get<AppService>(AppService);

      // Act
      const result1 = await service.getServiceDetails();
      const result2 = await service2.getServiceDetails();

      // Assert
      expect(result1).toEqual(result2);
    });
  });

  describe('Data validation', () => {
    it('should return data with non-empty version', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      expect(result.version).toBeTruthy();
      expect(result.version.length).toBeGreaterThan(0);
    });

    it('should return data with non-empty serviceName', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      expect(result.serviceName).toBeTruthy();
      expect(result.serviceName.length).toBeGreaterThan(0);
    });

    it('should return data with valid semantic version', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      const versionParts = result.version.split('.');
      expect(versionParts).toHaveLength(3);
      versionParts.forEach(part => {
        expect(parseInt(part)).not.toBeNaN();
        expect(parseInt(part)).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('Type safety', () => {
    it('should return Promise<AboutDto>', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      expect(result).toEqual(expect.objectContaining({
        version: expect.any(String),
        serviceName: expect.any(String)
      }));
    });

    it('should not return additional properties', async () => {
      // Act
      const result = await service.getServiceDetails();

      // Assert
      const keys = Object.keys(result);
      expect(keys).toHaveLength(2);
      expect(keys).toContain('version');
      expect(keys).toContain('serviceName');
    });
  });
});
