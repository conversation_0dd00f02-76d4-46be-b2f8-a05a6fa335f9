import {
  Catch,
  ArgumentsHost,
  HttpException,
  ExceptionFilter,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { MESSAGES } from '@nestjs/core/constants';
import { isObject } from 'class-validator';
import { LoggerService } from '../../loggers/logger.service';

const internalServerErrorResponseBody = {
  message: 'Nebula service could not complete the operation',
  error: MESSAGES.UNKNOWN_EXCEPTION_MESSAGE,
  statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
};

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    private readonly logger: LoggerService,
  ) {}

  catch(exception: any, host: ArgumentsHost) {
    this.logger.error(exception);

    const { httpAdapter } = this.httpAdapterHost;
    const httpContext = host.switchToHttp();
    let errorResponseBody: object;
    let httpStatus: HttpStatus;
    if (exception instanceof HttpException) {
      const res = exception.getResponse();
      httpStatus = exception.getStatus();
      if (httpStatus === HttpStatus.INTERNAL_SERVER_ERROR) {
        errorResponseBody = internalServerErrorResponseBody;
        if (res['message'] !== 'Internal Server Error') {
          errorResponseBody['message'] = res['message'];
        }
      } else {
        errorResponseBody = isObject(res)
          ? res
          : {
              statusCode: httpStatus,
              message: res,
            };
      }
    } else {
      httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
      errorResponseBody = internalServerErrorResponseBody;
    }
    this.logger.error(errorResponseBody);

    httpAdapter.reply(httpContext.getResponse(), errorResponseBody, httpStatus);
  }
}
