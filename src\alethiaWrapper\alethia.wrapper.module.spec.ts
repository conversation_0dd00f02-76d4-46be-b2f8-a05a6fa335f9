import { Test, TestingModule } from '@nestjs/testing';
import { AlethiaWrapperModule } from './alethia.wrapper.module';
import { AlethiaWrapperController } from './alethia.wrapper.controller';
import { AlethiaWrapperService } from './alethia.wrapper.service';
import { AlethiaService } from './alethia.service';
import { LoggerModule } from '../loggers/logger.module';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../loggers/logger.service';

// Mock external dependencies
jest.mock('../loggers/logger.module', () => ({
  LoggerModule: class MockLoggerModule {}
}));

jest.mock('../loggers/logger.service', () => ({
  LoggerService: jest.fn().mockImplementation(() => ({
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }))
}));

describe('AlethiaWrapperModule', () => {
  let module: TestingModule;
  let alethiaWrapperModule: AlethiaWrapperModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [AlethiaWrapperModule],
      providers: [
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                ALETHIA_BASE_URL: 'https://test-alethia.com',
                ALETHIA_USERNAME: 'testuser',
                ALETHIA_PASSWORD: 'testpass',
                BLUE_SOT_URL: 'https://test-blue-sot.com'
              };
              return config[key];
            })
          }
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn()
          }
        }
      ]
    }).compile();

    alethiaWrapperModule = module.get<AlethiaWrapperModule>(AlethiaWrapperModule);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe('Module instantiation', () => {
    it('should be defined', () => {
      expect(alethiaWrapperModule).toBeDefined();
    });

    it('should be an instance of AlethiaWrapperModule', () => {
      expect(alethiaWrapperModule).toBeInstanceOf(AlethiaWrapperModule);
    });
  });

  describe('Module configuration', () => {
    it('should have correct module metadata', () => {
      const moduleMetadata = Reflect.getMetadata('imports', AlethiaWrapperModule) || [];
      expect(moduleMetadata).toBeDefined();
      expect(Array.isArray(moduleMetadata)).toBe(true);
    });

    it('should import LoggerModule', () => {
      const imports = Reflect.getMetadata('imports', AlethiaWrapperModule) || [];
      expect(imports).toContain(LoggerModule);
    });

    it('should have correct controllers', () => {
      const controllers = Reflect.getMetadata('controllers', AlethiaWrapperModule) || [];
      expect(controllers).toContain(AlethiaWrapperController);
    });

    it('should have correct providers', () => {
      const providers = Reflect.getMetadata('providers', AlethiaWrapperModule) || [];
      expect(providers).toContain(AlethiaWrapperService);
      expect(providers).toContain(AlethiaService);
    });
  });

  describe('Controllers', () => {
    it('should register AlethiaWrapperController', () => {
      const controllers = Reflect.getMetadata('controllers', AlethiaWrapperModule) || [];
      expect(controllers).toContain(AlethiaWrapperController);
    });

    it('should have AlethiaWrapperController available in module', () => {
      const controller = module.get<AlethiaWrapperController>(AlethiaWrapperController);
      expect(controller).toBeDefined();
      expect(controller).toBeInstanceOf(AlethiaWrapperController);
    });
  });

  describe('Providers', () => {
    it('should register AlethiaWrapperService', () => {
      const providers = Reflect.getMetadata('providers', AlethiaWrapperModule) || [];
      expect(providers).toContain(AlethiaWrapperService);
    });

    it('should register AlethiaService', () => {
      const providers = Reflect.getMetadata('providers', AlethiaWrapperModule) || [];
      expect(providers).toContain(AlethiaService);
    });

    it('should have AlethiaWrapperService available in module', () => {
      const service = module.get<AlethiaWrapperService>(AlethiaWrapperService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(AlethiaWrapperService);
    });

    it('should have AlethiaService available in module', () => {
      const service = module.get<AlethiaService>(AlethiaService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(AlethiaService);
    });
  });

  describe('Dependencies', () => {
    it('should resolve all dependencies successfully', () => {
      expect(() => module.get<AlethiaWrapperController>(AlethiaWrapperController)).not.toThrow();
      expect(() => module.get<AlethiaWrapperService>(AlethiaWrapperService)).not.toThrow();
      expect(() => module.get<AlethiaService>(AlethiaService)).not.toThrow();
    });

    it('should have LoggerService available through LoggerModule', () => {
      const loggerService = module.get<LoggerService>(LoggerService);
      expect(loggerService).toBeDefined();
    });

    it('should inject dependencies correctly in services', () => {
      const alethiaWrapperService = module.get<AlethiaWrapperService>(AlethiaWrapperService);
      const alethiaService = module.get<AlethiaService>(AlethiaService);
      
      expect(alethiaWrapperService).toBeDefined();
      expect(alethiaService).toBeDefined();
    });
  });

  describe('Module lifecycle', () => {
    it('should initialize without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [AlethiaWrapperModule],
        providers: [
          {
            provide: ConfigService,
            useValue: {
              get: jest.fn(() => 'test-value')
            }
          },
          {
            provide: LoggerService,
            useValue: {
              log: jest.fn(),
              error: jest.fn()
            }
          }
        ]
      }).compile();

      await expect(testModule.init()).resolves.not.toThrow();
      await testModule.close();
    });

    it('should close without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [AlethiaWrapperModule],
        providers: [
          {
            provide: ConfigService,
            useValue: {
              get: jest.fn(() => 'test-value')
            }
          },
          {
            provide: LoggerService,
            useValue: {
              log: jest.fn(),
              error: jest.fn()
            }
          }
        ]
      }).compile();

      await testModule.init();
      await expect(testModule.close()).resolves.not.toThrow();
    });
  });

  describe('Integration tests', () => {
    it('should create a complete module context', async () => {
      const testModule = await Test.createTestingModule({
        imports: [AlethiaWrapperModule],
        providers: [
          {
            provide: ConfigService,
            useValue: {
              get: jest.fn(() => 'test-value')
            }
          },
          {
            provide: LoggerService,
            useValue: {
              log: jest.fn(),
              error: jest.fn()
            }
          }
        ]
      }).compile();

      const app = testModule.createNestApplication();
      await expect(app.init()).resolves.not.toThrow();
      
      await app.close();
      await testModule.close();
    });

    it('should have all required services available for controller', async () => {
      const controller = module.get<AlethiaWrapperController>(AlethiaWrapperController);
      const wrapperService = module.get<AlethiaWrapperService>(AlethiaWrapperService);
      
      expect(controller).toBeDefined();
      expect(wrapperService).toBeDefined();
    });

    it('should have all required services available for wrapper service', async () => {
      const wrapperService = module.get<AlethiaWrapperService>(AlethiaWrapperService);
      const alethiaService = module.get<AlethiaService>(AlethiaService);
      const loggerService = module.get<LoggerService>(LoggerService);
      
      expect(wrapperService).toBeDefined();
      expect(alethiaService).toBeDefined();
      expect(loggerService).toBeDefined();
    });
  });

  describe('Module structure validation', () => {
    it('should have exactly one controller', () => {
      const controllers = Reflect.getMetadata('controllers', AlethiaWrapperModule) || [];
      expect(controllers).toHaveLength(1);
    });

    it('should have exactly two providers', () => {
      const providers = Reflect.getMetadata('providers', AlethiaWrapperModule) || [];
      expect(providers).toHaveLength(2);
    });

    it('should have exactly one import', () => {
      const imports = Reflect.getMetadata('imports', AlethiaWrapperModule) || [];
      expect(imports).toHaveLength(1);
    });

    it('should not have any exports', () => {
      const exports = Reflect.getMetadata('exports', AlethiaWrapperModule) || [];
      expect(exports).toHaveLength(0);
    });
  });

  describe('Error scenarios', () => {
    it('should handle missing dependencies gracefully in test environment', async () => {
      // Test that the module can still be created even with minimal mocking
      const testModule = await Test.createTestingModule({
        imports: [AlethiaWrapperModule],
        providers: [
          {
            provide: ConfigService,
            useValue: { get: jest.fn() }
          },
          {
            provide: LoggerService,
            useValue: { log: jest.fn(), error: jest.fn() }
          }
        ]
      }).compile();

      expect(testModule).toBeDefined();
      await testModule.close();
    });
  });

  describe('Type safety', () => {
    it('should have correct TypeScript types', () => {
      expect(alethiaWrapperModule).toBeInstanceOf(Object);
      expect(typeof AlethiaWrapperModule).toBe('function');
    });

    it('should be a proper NestJS module', () => {
      const moduleDecorator = Reflect.getMetadata('__module__', AlethiaWrapperModule);
      expect(moduleDecorator).toBeDefined();
    });
  });
});
