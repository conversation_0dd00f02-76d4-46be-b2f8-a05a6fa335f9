import { Body, Controller, Delete, Get, Post, Query } from '@nestjs/common';
import { AlethiaWrapperService } from './alethia.wrapper.service';
import { AlethiaService } from './alethia.service';
import { LoggerService } from '../loggers/logger.service';

@Controller('alethia')
export class AlethiaWrapperController {
  constructor(
    private readonly alethiaService: AlethiaService,
    private readonly alethiaWrapperService: AlethiaWrapperService,
    private readonly logger: LoggerService,
  ) {}

  @Post('owners')
  async createAlethiaRequest(@Body() data) {
    this.logger.log('alethia get owners request:', data);
    const result = await this.alethiaWrapperService.getOwners(data);
    return result;
  }
}
