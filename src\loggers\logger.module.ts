// src/logger/logger.module.ts
import { Global, Module } from '@nestjs/common';
import { LoggerService } from './logger.service';
import { randomUUID } from 'crypto';
import * as noir from 'pino-noir';
import { LoggerModule as PinoLoggerModule } from 'nestjs-pino';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../utils/constants';

@Global()
@Module({
  imports: [
    PinoLoggerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        pinoHttp: {
          level: configService.get<string>(ENVIRONMENT_VARS.LOG_LEVEL),
          // * Set this property to the desired logging level. In order of priority, available levels are:
          // *
          // * - 'fatal'
          // * - 'error'
          // * - 'warn'
          // * - 'info'
          // * - 'debug'
          // * - 'trace'
          // *
          // * The logging level is a __minimum__ level. For instance if `logger.level` is `'info'` then all `'fatal'`, `'error'`, `'warn'`,
          // * and `'info'` logs will be enabled.
          // *
          // * You can pass `'silent'` to disable logging.
          // autoLogging: false,  //Logs response as well

          serializers: noir(
            [
              'req.headers.authorization',
              'err.config.headers.authorization',
              'err.options.cause.config.headers.Authorization',
              'err.options.cause.config.httpsAgent.headers.Authorization',
            ],
            'Ssshh!',
          ),
          quietReqLogger: true, //To stop logging req multipletimes.
          formatters: {
            // bindings: (bindings) => {
            //   return { corelationId: randomUUID() };
            // },
            level: (label) => {
              return { level: label.toUpperCase() };
            },
          },
          redact: {
            paths: ['pid', 'hostname'],
            censor: '[PINO REDACTED]',
            remove: true,
          },
          // customProps: (req, res) => {
          //   return {
          //     correlationid: (req as any).correlationid,
          //   };
          // },
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [LoggerService],
  exports: [LoggerService, PinoLoggerModule],
})
export class LoggerModule {}
