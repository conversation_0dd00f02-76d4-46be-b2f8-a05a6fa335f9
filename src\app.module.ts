import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import * as Joi from 'joi';
import { APP_FILTER } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AllExceptionsFilter } from './utils/filters/allException.filter';
import { LoggerMiddleware } from './loggers/logger.middleware';
import { LoggerModule } from './loggers/logger.module';
import { AlethiaWrapperModule } from './alethiaWrapper/alethia.wrapper.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      cache: true,
      isGlobal: true,
      validationSchema: Joi.object({
        PORT: Joi.string().required(),
        GLOBAL_ROUTE_PREFIX: Joi.string().required(),
        REQUEST_BODY_SIZE_LIMIT: Joi.string().default('5mb'),
      }),
    }),
    LoggerModule,
    AlethiaWrapperModule,
  ],
  controllers: [AppController],
  providers: [
    { provide: APP_FILTER, useClass: AllExceptionsFilter },
    AppService,
  ],
  exports: [],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
