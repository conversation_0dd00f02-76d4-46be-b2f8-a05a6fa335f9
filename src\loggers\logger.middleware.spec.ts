import { Test, TestingModule } from '@nestjs/testing';
import { LoggerMiddleware } from './logger.middleware';
import { Logger } from 'nestjs-pino';
import { Request, Response, NextFunction } from 'express';

describe('LoggerMiddleware', () => {
  let middleware: LoggerMiddleware;
  let logger: Logger;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
    fatal: jest.fn(),
    trace: jest.fn(),
    info: jest.fn(),
    setContext: jest.fn(),
    localInstance: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoggerMiddleware,
        {
          provide: Logger,
          useValue: mockLogger
        }
      ],
    }).compile();

    middleware = module.get<LoggerMiddleware>(LoggerMiddleware);
    logger = module.get<Logger>(Logger);

    // Reset mocks
    mockRequest = {
      headers: {},
      method: 'GET',
      url: '/test',
      body: {},
      query: {},
      params: {}
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis(),
      setHeader: jest.fn().mockReturnThis(),
      getHeader: jest.fn(),
      locals: {}
    };

    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Middleware instantiation', () => {
    it('should be defined', () => {
      expect(middleware).toBeDefined();
    });

    it('should be an instance of LoggerMiddleware', () => {
      expect(middleware).toBeInstanceOf(LoggerMiddleware);
    });

    it('should implement NestMiddleware interface', () => {
      expect(middleware).toHaveProperty('use');
      expect(typeof middleware.use).toBe('function');
    });

    it('should have Injectable decorator', () => {
      const injectable = Reflect.getMetadata('__injectable__', LoggerMiddleware);
      expect(injectable).toBe(true);
    });

    it('should inject Logger dependency', () => {
      expect(logger).toBeDefined();
      expect(logger).toBe(mockLogger);
    });
  });

  describe('use method', () => {
    it('should call next function', () => {
      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should call next function without arguments', () => {
      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith();
      expect(mockNext.mock.calls[0]).toHaveLength(0);
    });

    it('should not modify request headers', () => {
      // Arrange
      const originalHeaders = { ...mockRequest.headers };

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockRequest.headers).toEqual(originalHeaders);
    });

    it('should not modify response', () => {
      // Arrange
      const originalResponse = { ...mockResponse };

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockResponse).toEqual(originalResponse);
    });

    it('should handle request with existing headers', () => {
      // Arrange
      mockRequest.headers = {
        'content-type': 'application/json',
        'authorization': 'Bearer token123',
        'x-custom-header': 'custom-value'
      };

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockRequest.headers['content-type']).toBe('application/json');
      expect(mockRequest.headers['authorization']).toBe('Bearer token123');
      expect(mockRequest.headers['x-custom-header']).toBe('custom-value');
    });

    it('should handle request with no headers', () => {
      // Arrange
      mockRequest.headers = undefined;

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should handle request with empty headers object', () => {
      // Arrange
      mockRequest.headers = {};

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockRequest.headers).toEqual({});
    });
  });

  describe('Method signature and parameters', () => {
    it('should accept Request, Response, and NextFunction parameters', () => {
      // Act & Assert
      expect(() => {
        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      }).not.toThrow();
    });

    it('should work with different request methods', () => {
      const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'];

      methods.forEach(method => {
        mockRequest.method = method;
        mockNext.mockClear();

        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

        expect(mockNext).toHaveBeenCalledTimes(1);
      });
    });

    it('should work with different request URLs', () => {
      const urls = ['/api/test', '/health', '/about', '/', '/api/v1/users/123'];

      urls.forEach(url => {
        mockRequest.url = url;
        mockNext.mockClear();

        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

        expect(mockNext).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Error handling', () => {
    it('should not throw errors when next function throws', () => {
      // Arrange
      mockNext.mockImplementation(() => {
        throw new Error('Next function error');
      });

      // Act & Assert
      expect(() => {
        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      }).toThrow('Next function error');
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should handle null request', () => {
      // Act & Assert
      expect(() => {
        middleware.use(null as any, mockResponse as Response, mockNext);
      }).not.toThrow();
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should handle null response', () => {
      // Act & Assert
      expect(() => {
        middleware.use(mockRequest as Request, null as any, mockNext);
      }).not.toThrow();
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should handle undefined next function', () => {
      // Act & Assert
      expect(() => {
        middleware.use(mockRequest as Request, mockResponse as Response, undefined as any);
      }).toThrow();
    });
  });

  describe('Performance characteristics', () => {
    it('should execute quickly', () => {
      // Arrange
      const startTime = Date.now();

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      const endTime = Date.now();

      // Assert
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(10); // Should complete in less than 10ms
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should handle multiple rapid calls', () => {
      // Arrange
      const callCount = 100;
      const startTime = Date.now();

      // Act
      for (let i = 0; i < callCount; i++) {
        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      }
      const endTime = Date.now();

      // Assert
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(100); // Should complete 100 calls in less than 100ms
      expect(mockNext).toHaveBeenCalledTimes(callCount);
    });
  });

  describe('Integration scenarios', () => {
    it('should work in a middleware chain', () => {
      // Arrange
      let middlewareChainExecuted = false;
      mockNext.mockImplementation(() => {
        middlewareChainExecuted = true;
      });

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(middlewareChainExecuted).toBe(true);
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should not interfere with request processing', () => {
      // Arrange
      mockRequest.body = { data: 'test' };
      mockRequest.query = { param: 'value' };
      mockRequest.params = { id: '123' };

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockRequest.body).toEqual({ data: 'test' });
      expect(mockRequest.query).toEqual({ param: 'value' });
      expect(mockRequest.params).toEqual({ id: '123' });
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should work with async next function', async () => {
      // Arrange
      let asyncExecuted = false;
      mockNext.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 1));
        asyncExecuted = true;
      });

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      await new Promise(resolve => setTimeout(resolve, 10)); // Wait for async execution

      // Assert
      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(asyncExecuted).toBe(true);
    });
  });

  describe('Commented code behavior', () => {
    it('should not set X-Correlation-ID header (commented out)', () => {
      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockRequest.headers).not.toHaveProperty('X-Correlation-ID');
      expect(mockRequest.headers).not.toHaveProperty('x-correlation-id');
    });

    it('should not generate UUID (commented out)', () => {
      // Arrange
      const originalHeaders = { ...mockRequest.headers };

      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockRequest.headers).toEqual(originalHeaders);
    });
  });

  describe('Type safety', () => {
    it('should implement NestMiddleware interface correctly', () => {
      expect(middleware).toHaveProperty('use');
      expect(typeof middleware.use).toBe('function');
      expect(middleware.use.length).toBe(3); // Should accept 3 parameters
    });

    it('should work with proper TypeScript types', () => {
      // This test ensures the middleware works with proper Express types
      const req = mockRequest as Request;
      const res = mockResponse as Response;
      const next = mockNext as NextFunction;

      expect(() => {
        middleware.use(req, res, next);
      }).not.toThrow();
    });
  });

  describe('Logger dependency', () => {
    it('should have logger injected but not use it', () => {
      // Act
      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(logger).toBeDefined();
      // Verify that logger methods are not called since the middleware doesn't use them
      expect(mockLogger.log).not.toHaveBeenCalled();
      expect(mockLogger.error).not.toHaveBeenCalled();
      expect(mockLogger.warn).not.toHaveBeenCalled();
      expect(mockLogger.debug).not.toHaveBeenCalled();
    });
  });
});
