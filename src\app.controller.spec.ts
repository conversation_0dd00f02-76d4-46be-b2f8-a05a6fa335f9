import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AboutDto } from './about.response.dto';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  const mockAboutDto: AboutDto = {
    version: '1.0.0',
    serviceName: 'nebula-alethia-mgmt-service'
  };

  const mockAppService = {
    getServiceDetails: jest.fn()
  };

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        {
          provide: AppService,
          useValue: mockAppService
        }
      ],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should be defined', () => {
      expect(appController).toBeDefined();
    });

    it('should have appService injected', () => {
      expect(appService).toBeDefined();
      expect(appService).toBe(mockAppService);
    });
  });

  describe('getServiceDetails', () => {
    it('should return service details', async () => {
      // Arrange
      mockAppService.getServiceDetails.mockResolvedValue(mockAboutDto);

      // Act
      const result = await appController.getServiceDetails();

      // Assert
      expect(result).toEqual(mockAboutDto);
      expect(mockAppService.getServiceDetails).toHaveBeenCalledTimes(1);
      expect(mockAppService.getServiceDetails).toHaveBeenCalledWith();
    });

    it('should return AboutDto type', async () => {
      // Arrange
      mockAppService.getServiceDetails.mockResolvedValue(mockAboutDto);

      // Act
      const result = await appController.getServiceDetails();

      // Assert
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('serviceName');
      expect(typeof result.version).toBe('string');
      expect(typeof result.serviceName).toBe('string');
    });

    it('should handle service returning different data', async () => {
      // Arrange
      const differentData = {
        version: '2.1.0',
        serviceName: 'different-service'
      };
      mockAppService.getServiceDetails.mockResolvedValue(differentData);

      // Act
      const result = await appController.getServiceDetails();

      // Assert
      expect(result).toEqual(differentData);
      expect(result.version).toBe('2.1.0');
      expect(result.serviceName).toBe('different-service');
    });

    it('should propagate errors from service', async () => {
      // Arrange
      const error = new Error('Service error');
      mockAppService.getServiceDetails.mockRejectedValue(error);

      // Act & Assert
      await expect(appController.getServiceDetails()).rejects.toThrow('Service error');
      expect(mockAppService.getServiceDetails).toHaveBeenCalledTimes(1);
    });

    it('should handle service returning null', async () => {
      // Arrange
      mockAppService.getServiceDetails.mockResolvedValue(null);

      // Act
      const result = await appController.getServiceDetails();

      // Assert
      expect(result).toBeNull();
      expect(mockAppService.getServiceDetails).toHaveBeenCalledTimes(1);
    });

    it('should handle service returning undefined', async () => {
      // Arrange
      mockAppService.getServiceDetails.mockResolvedValue(undefined);

      // Act
      const result = await appController.getServiceDetails();

      // Assert
      expect(result).toBeUndefined();
      expect(mockAppService.getServiceDetails).toHaveBeenCalledTimes(1);
    });

    it('should be an async function', () => {
      // Arrange
      mockAppService.getServiceDetails.mockResolvedValue(mockAboutDto);

      // Act
      const result = appController.getServiceDetails();

      // Assert
      expect(result).toBeInstanceOf(Promise);
    });

    it('should call service method exactly once per call', async () => {
      // Arrange
      mockAppService.getServiceDetails.mockResolvedValue(mockAboutDto);

      // Act
      await appController.getServiceDetails();
      await appController.getServiceDetails();
      await appController.getServiceDetails();

      // Assert
      expect(mockAppService.getServiceDetails).toHaveBeenCalledTimes(3);
    });
  });

  describe('Controller Metadata', () => {
    it('should have correct controller decorator', () => {
      const controllerMetadata = Reflect.getMetadata('path', AppController);
      expect(controllerMetadata).toBe('');
    });

    it('should have getServiceDetails method with correct decorators', () => {
      const method = appController.getServiceDetails;
      expect(method).toBeDefined();
      expect(typeof method).toBe('function');
    });
  });

  describe('Integration scenarios', () => {
    it('should work with real service data structure', async () => {
      // Arrange
      const realServiceData = {
        version: '1.0.0',
        serviceName: 'nebula-alethia-mgmt-service'
      };
      mockAppService.getServiceDetails.mockResolvedValue(realServiceData);

      // Act
      const result = await appController.getServiceDetails();

      // Assert
      expect(result.version).toMatch(/^\d+\.\d+\.\d+$/);
      expect(result.serviceName).toContain('nebula');
      expect(result.serviceName).toContain('alethia');
    });

    it('should handle concurrent requests', async () => {
      // Arrange
      mockAppService.getServiceDetails.mockResolvedValue(mockAboutDto);

      // Act
      const promises = Array(5).fill(null).map(() => appController.getServiceDetails());
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toEqual(mockAboutDto);
      });
      expect(mockAppService.getServiceDetails).toHaveBeenCalledTimes(5);
    });
  });

  describe('Error handling', () => {
    it('should handle service throwing synchronous error', async () => {
      // Arrange
      mockAppService.getServiceDetails.mockImplementation(() => {
        throw new Error('Synchronous error');
      });

      // Act & Assert
      await expect(appController.getServiceDetails()).rejects.toThrow('Synchronous error');
    });

    it('should handle service throwing custom error', async () => {
      // Arrange
      class CustomError extends Error {
        constructor(message: string) {
          super(message);
          this.name = 'CustomError';
        }
      }
      const customError = new CustomError('Custom error message');
      mockAppService.getServiceDetails.mockRejectedValue(customError);

      // Act & Assert
      await expect(appController.getServiceDetails()).rejects.toThrow(CustomError);
      await expect(appController.getServiceDetails()).rejects.toThrow('Custom error message');
    });
  });

  describe('Type safety', () => {
    it('should return Promise<AboutDto>', async () => {
      // Arrange
      mockAppService.getServiceDetails.mockResolvedValue(mockAboutDto);

      // Act
      const result = await appController.getServiceDetails();

      // Assert
      expect(result).toEqual(expect.objectContaining({
        version: expect.any(String),
        serviceName: expect.any(String)
      }));
    });
  });
});
