import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AlethiaService } from './alethia.service';
import { LoggerService } from '../loggers/logger.service';
import { withResponseErrorHandler } from '../utils/helpers';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock the helper function
jest.mock('../utils/helpers', () => ({
  withResponseErrorHandler: jest.fn()
}));

const mockedWithResponseErrorHandler = withResponseErrorHandler as jest.MockedFunction<typeof withResponseErrorHandler>;

describe('AlethiaService', () => {
  let service: AlethiaService;
  let configService: ConfigService;
  let loggerService: LoggerService;

  const mockConfigService = {
    get: jest.fn()
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  };

  beforeEach(async () => {
    // Set up environment variables
    process.env.ALETHIA_BASE_URL = 'https://test-alethia.com';
    process.env.ALETHIA_USERNAME = 'testuser';
    process.env.ALETHIA_PASSWORD = 'testpass';

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AlethiaService,
        {
          provide: ConfigService,
          useValue: mockConfigService
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService
        }
      ],
    }).compile();

    service = module.get<AlethiaService>(AlethiaService);
    configService = module.get<ConfigService>(ConfigService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.ALETHIA_BASE_URL;
    delete process.env.ALETHIA_USERNAME;
    delete process.env.ALETHIA_PASSWORD;
  });

  describe('Service instantiation', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should be an instance of AlethiaService', () => {
      expect(service).toBeInstanceOf(AlethiaService);
    });

    it('should have Injectable decorator', () => {
      const injectable = Reflect.getMetadata('__injectable__', AlethiaService);
      expect(injectable).toBe(true);
    });

    it('should inject dependencies correctly', () => {
      expect(configService).toBeDefined();
      expect(loggerService).toBeDefined();
    });
  });

  describe('getDeviceOwnersFromAlethia', () => {
    const mockDevices = [
      { id: 1, name: 'device1' },
      { id: 2, name: 'device2' }
    ];

    const mockTopologyData = [
      { name: 'host1.example.com' },
      { name: 'host2.example.com' }
    ];

    const mockTokenResponse = { access_token: 'mock-token-123' };
    const mockAlethiaResponse = {
      data: {
        allFiremonDevices: {
          nodes: [
            { hostname: 'host1.example.com', contact: '<EMAIL>' },
            { hostname: 'host2.example.com', contact: '<EMAIL>' }
          ]
        }
      }
    };

    beforeEach(() => {
      // Mock getAlethiaToken
      jest.spyOn(service, 'getAlethiaToken').mockResolvedValue('mock-token-123');
      
      // Mock withResponseErrorHandler for the main request
      mockedWithResponseErrorHandler.mockResolvedValue(mockAlethiaResponse);
    });

    it('should get device owners successfully', async () => {
      // Act
      const result = await service.getDeviceOwnersFromAlethia(mockDevices, mockTopologyData);

      // Assert
      expect(result).toEqual(mockAlethiaResponse);
      expect(loggerService.log).toHaveBeenCalledWith('Received request to get device owners');
      expect(loggerService.log).toHaveBeenCalledWith('Request payload to get device owners', {
        devices: mockDevices,
        topologyData: mockTopologyData
      });
    });

    it('should call getAlethiaToken', async () => {
      // Act
      await service.getDeviceOwnersFromAlethia(mockDevices, mockTopologyData);

      // Assert
      expect(service.getAlethiaToken).toHaveBeenCalledTimes(1);
    });

    it('should create correct GraphQL payload', async () => {
      // Act
      await service.getDeviceOwnersFromAlethia(mockDevices, mockTopologyData);

      // Assert
      expect(mockedWithResponseErrorHandler).toHaveBeenCalledWith(
        expect.any(Promise)
      );
    });

    it('should use correct headers', async () => {
      // Act
      await service.getDeviceOwnersFromAlethia(mockDevices, mockTopologyData);

      // Assert
      const axiosCall = mockedAxios.post.mock.calls[0];
      expect(axiosCall[2].headers).toEqual({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token-123'
      });
    });

    it('should use correct URL', async () => {
      // Act
      await service.getDeviceOwnersFromAlethia(mockDevices, mockTopologyData);

      // Assert
      const axiosCall = mockedAxios.post.mock.calls[0];
      expect(axiosCall[0]).toBe('https://test-alethia.com/graphql');
    });

    it('should log the payload and response', async () => {
      // Act
      await service.getDeviceOwnersFromAlethia(mockDevices, mockTopologyData);

      // Assert
      expect(loggerService.log).toHaveBeenCalledWith('Calling Alethia service with payload', expect.any(String));
      expect(loggerService.log).toHaveBeenCalledWith('Received alethia response', mockAlethiaResponse);
    });

    it('should handle empty topology data', async () => {
      // Act
      const result = await service.getDeviceOwnersFromAlethia(mockDevices, []);

      // Assert
      expect(result).toEqual(mockAlethiaResponse);
      expect(service.getAlethiaToken).toHaveBeenCalled();
    });

    it('should handle empty devices array', async () => {
      // Act
      const result = await service.getDeviceOwnersFromAlethia([], mockTopologyData);

      // Assert
      expect(result).toEqual(mockAlethiaResponse);
      expect(service.getAlethiaToken).toHaveBeenCalled();
    });
  });

  describe('createAlethiaPayload (private method)', () => {
    const mockTopologyData = [
      { name: 'host1.example.com' },
      { name: 'host2.example.com' },
      { name: 'host3.example.com' }
    ];

    it('should create correct GraphQL query', async () => {
      // We need to test this indirectly through getDeviceOwnersFromAlethia
      jest.spyOn(service, 'getAlethiaToken').mockResolvedValue('mock-token');
      mockedWithResponseErrorHandler.mockResolvedValue({ data: {} });

      await service.getDeviceOwnersFromAlethia([], mockTopologyData);

      // Check that the axios call was made with the correct query structure
      const axiosCall = mockedAxios.post.mock.calls[0];
      const payload = axiosCall[1];
      
      expect(payload).toHaveProperty('query');
      expect(payload.query).toContain('allFiremonDevices');
      expect(payload.query).toContain('host1.example.com');
      expect(payload.query).toContain('host2.example.com');
      expect(payload.query).toContain('host3.example.com');
    });

    it('should handle single hostname', async () => {
      const singleHostData = [{ name: 'single-host.com' }];
      
      jest.spyOn(service, 'getAlethiaToken').mockResolvedValue('mock-token');
      mockedWithResponseErrorHandler.mockResolvedValue({ data: {} });

      await service.getDeviceOwnersFromAlethia([], singleHostData);

      const axiosCall = mockedAxios.post.mock.calls[0];
      const payload = axiosCall[1];
      
      expect(payload.query).toContain('single-host.com');
    });

    it('should log the created payload', async () => {
      jest.spyOn(service, 'getAlethiaToken').mockResolvedValue('mock-token');
      mockedWithResponseErrorHandler.mockResolvedValue({ data: {} });

      await service.getDeviceOwnersFromAlethia([], mockTopologyData);

      expect(loggerService.log).toHaveBeenCalledWith('payload for Alethia', expect.any(String));
    });
  });

  describe('getAlethiaToken', () => {
    const mockTokenResponse = { access_token: 'test-access-token-123' };

    beforeEach(() => {
      mockedWithResponseErrorHandler.mockResolvedValue(mockTokenResponse);
    });

    it('should get token successfully', async () => {
      // Act
      const result = await service.getAlethiaToken();

      // Assert
      expect(result).toBe('test-access-token-123');
      expect(loggerService.log).toHaveBeenCalledWith('inside getAlethiaToken');
      expect(loggerService.log).toHaveBeenCalledWith('Calling Alethia token with payload');
      expect(loggerService.log).toHaveBeenCalledWith('Received alethia token', 'test-access-token-123');
    });

    it('should use correct URL for token request', async () => {
      // Act
      await service.getAlethiaToken();

      // Assert
      const axiosCall = mockedAxios.post.mock.calls[0];
      expect(axiosCall[0]).toBe('https://test-alethia.com/api/token');
    });

    it('should use correct payload for token request', async () => {
      // Act
      await service.getAlethiaToken();

      // Assert
      const axiosCall = mockedAxios.post.mock.calls[0];
      expect(axiosCall[1]).toEqual({
        username: 'testuser',
        password: 'testpass'
      });
    });

    it('should use correct headers for token request', async () => {
      // Act
      await service.getAlethiaToken();

      // Assert
      const axiosCall = mockedAxios.post.mock.calls[0];
      expect(axiosCall[2].headers).toEqual({
        'Content-Type': 'application/x-www-form-urlencoded'
      });
    });

    it('should handle token response with different structure', async () => {
      const differentTokenResponse = { access_token: 'different-token' };
      mockedWithResponseErrorHandler.mockResolvedValue(differentTokenResponse);

      // Act
      const result = await service.getAlethiaToken();

      // Assert
      expect(result).toBe('different-token');
    });

    it('should handle missing environment variables', async () => {
      delete process.env.ALETHIA_USERNAME;
      delete process.env.ALETHIA_PASSWORD;

      // Act
      await service.getAlethiaToken();

      // Assert
      const axiosCall = mockedAxios.post.mock.calls[0];
      expect(axiosCall[1]).toEqual({
        username: undefined,
        password: undefined
      });
    });

    it('should call withResponseErrorHandler', async () => {
      // Act
      await service.getAlethiaToken();

      // Assert
      expect(mockedWithResponseErrorHandler).toHaveBeenCalledWith(expect.any(Promise));
    });
  });

  describe('Error handling', () => {
    it('should propagate errors from getAlethiaToken', async () => {
      const error = new Error('Token request failed');
      jest.spyOn(service, 'getAlethiaToken').mockRejectedValue(error);

      await expect(service.getDeviceOwnersFromAlethia([], [])).rejects.toThrow('Token request failed');
    });

    it('should propagate errors from withResponseErrorHandler', async () => {
      jest.spyOn(service, 'getAlethiaToken').mockResolvedValue('mock-token');
      const error = new Error('API request failed');
      mockedWithResponseErrorHandler.mockRejectedValue(error);

      await expect(service.getDeviceOwnersFromAlethia([], [])).rejects.toThrow('API request failed');
    });

    it('should handle network errors', async () => {
      jest.spyOn(service, 'getAlethiaToken').mockResolvedValue('mock-token');
      const networkError = new Error('Network Error');
      mockedWithResponseErrorHandler.mockRejectedValue(networkError);

      await expect(service.getDeviceOwnersFromAlethia([], [])).rejects.toThrow('Network Error');
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complete workflow', async () => {
      const devices = [{ id: 1, name: 'device1' }];
      const topologyData = [{ name: 'host1.com' }];
      const tokenResponse = { access_token: 'integration-token' };
      const apiResponse = { data: { allFiremonDevices: { nodes: [] } } };

      // Mock the token call
      mockedWithResponseErrorHandler
        .mockResolvedValueOnce(tokenResponse)  // First call for token
        .mockResolvedValueOnce(apiResponse);   // Second call for API

      // Remove the spy to test the real method
      jest.restoreAllMocks();
      mockedWithResponseErrorHandler
        .mockResolvedValueOnce(tokenResponse)
        .mockResolvedValueOnce(apiResponse);

      const result = await service.getDeviceOwnersFromAlethia(devices, topologyData);

      expect(result).toEqual(apiResponse);
    });
  });

  describe('Performance and reliability', () => {
    it('should handle large topology data', async () => {
      const largeTopologyData = Array(100).fill(null).map((_, i) => ({ name: `host${i}.com` }));

      jest.spyOn(service, 'getAlethiaToken').mockResolvedValue('mock-token');
      mockedWithResponseErrorHandler.mockResolvedValue({ data: {} });

      await expect(service.getDeviceOwnersFromAlethia([], largeTopologyData)).resolves.not.toThrow();
    });

    it('should handle concurrent requests', async () => {
      jest.spyOn(service, 'getAlethiaToken').mockResolvedValue('mock-token');
      mockedWithResponseErrorHandler.mockResolvedValue({ data: {} });

      const promises = Array(5).fill(null).map(() =>
        service.getDeviceOwnersFromAlethia([], [{ name: 'test.com' }])
      );

      await expect(Promise.all(promises)).resolves.not.toThrow();
    });
  });
});
